import { defineBackend } from '@aws-amplify/backend';
import { auth } from './auth/resource';
import { data } from './data/resource';
import { sayHello } from './functions/say-hello/resource';
import { storage } from './storage/resource';
import { postSignUp } from './functions/postSignUpTrigger/resource';
import { addUserToGroup } from './functions/addUserToGroup/resource';
import { checkEmail } from './functions/checkEmail/resource';
import { trackLoginAttempt } from './functions/trackLoginAttempt/resource';

/**
 * @see https://docs.amplify.aws/react/build-a-backend/ to add storage, functions, and more
 */
const backend = defineBackend({
  sayHello,
  auth,
  data,
  addUserToGroup,
  checkEmail,
  storage,
  postSignUp,
  trackLoginAttempt,
});

// Add environment variables to the addUserToGroup function
backend.addUserToGroup.addEnvironment(
  'USER_POOL_ID',
  backend.auth.resources.userPool.userPoolId
);

// Add environment variables to the checkEmail function
backend.checkEmail.addEnvironment(
  'USER_POOL_ID',
  backend.auth.resources.userPool.userPoolId
);

// Grant the postConfirmation function access to the data resources
// backend.data.resources.graphqlApi.grantMutation(
//   backend.postConfirmation.resources.lambda,
//   'User'
// );
// backend.data.resources.graphqlApi.grantMutation(
//   backend.postConfirmation.resources.lambda,
//   'UserOnboarding'
// );

// extract L1 CfnUserPool resources
const { cfnUserPool, cfnUserPoolClient } = backend.auth.resources.cfnResources;
// modify cfnUserPool policies directly
cfnUserPool.policies = {
  passwordPolicy: {
    minimumLength: 12,
    requireLowercase: true,
    requireNumbers: true,
    requireSymbols: true,
    requireUppercase: true,
    temporaryPasswordValidityDays: 3,
  },
};

// Enable device tracking
cfnUserPool.deviceConfiguration = {
  challengeRequiredOnNewDevice: false,
  deviceOnlyRememberedOnUserPrompt: false,
};

if (cfnUserPoolClient) {
  cfnUserPoolClient.accessTokenValidity = 1;
  cfnUserPoolClient.idTokenValidity = 1;
  cfnUserPoolClient.refreshTokenValidity = 30;
  cfnUserPoolClient.tokenValidityUnits = {
    accessToken: 'hours',
    idToken: 'hours',
    refreshToken: 'days',
  };
}
