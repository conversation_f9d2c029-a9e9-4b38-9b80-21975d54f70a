import { a } from '@aws-amplify/backend';

/**
 * Living document related models
 */

export const LivingDocument = a
  .model({
    id: a.id(),
    userId: a.string().required(),
    documentType: a.enum([
      'EmergencyContacts',
      'PetCare',
      'DigitalAssets',
      'EndOfLifeWishes',
      'MedicalDirectives',
      'Other',
    ]),
    title: a.string().required(),
    content: a.string().required(), // JSON string containing document data
    version: a.integer().default(1),
    status: a.enum(['Draft', 'Active', 'Archived']),
    lastReviewDate: a.datetime(),
    nextReviewDate: a.datetime(),
    reminderFrequency: a.enum([
      'Monthly',
      'Quarterly',
      'SemiAnnually',
      'Annually',
    ]),
    isTemplate: a.boolean().default(false),
    templateId: a.string(),
    createdAt: a.datetime(),
    updatedAt: a.datetime(),
  })
  .identifier(['id'])
  .authorization(allow => [allow.owner()]);

export const Document = a
  .model({
    id: a.id(),
    title: a.string().required(),
    type: a.enum(['Will', 'Trust', 'POA', 'Other']),
    status: a.enum(['draft', 'signed', 'shipped', 'received', 'archived']),
    dateCreated: a.datetime().required(),
    lastModified: a.datetime(),
    version: a.string().required(),
    content: a.string().required(),
    userId: a.string().required(),
    fileUrl: a.string(),
    trackingNumber: a.string(),
    signatureType: a.enum(['manual', 'electronic', 'notarized']),
    executionDate: a.datetime(),
    createdAt: a.datetime(),
    updatedAt: a.datetime(),
    // Relationships
    user: a.belongsTo('User', 'userId'),
    assignedWelonTrustId: a.string(),
  })
  .identifier(['id'])
  .authorization(allow => [
    allow.owner(),
    allow.ownerDefinedIn('assignedWelonTrustId').to(['read']),
    allow.group('ADMINS'),
  ]);

export const DocumentUpdateLog = a
  .model({
    id: a.id(),
    documentId: a.string().required(),
    userId: a.string().required(),
    changeType: a.enum(['Created', 'Updated', 'Reviewed', 'Archived']),
    changeDescription: a.string(),
    previousVersion: a.integer(),
    newVersion: a.integer(),
    timestamp: a.datetime().required(),
  })
  .identifier(['id'])
  .authorization(allow => [allow.owner()]);
