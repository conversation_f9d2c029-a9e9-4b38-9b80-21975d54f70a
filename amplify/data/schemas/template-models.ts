import { a } from '@aws-amplify/backend';

/**
 * Template management related models
 */

export const Template = a
  .model({
    id: a.string().required(),
    createdBy: a.string().required(),
    createdAt: a.datetime().required(),
    updatedAt: a.datetime().required(),
    type: a.string().required(),
    templateState: a.string().required(),
    templateName: a.string().required(),
    isActive: a.boolean().default(true),
  })
  .authorization(allow => [allow.authenticated(), allow.owner()]);

export const TemplateVersion = a
  .model({
    templateId: a.string().required(),
    versionNumber: a.integer().required(),
    content: a.string().required(),
    createdAt: a.datetime().required(),
  })
  .identifier(['templateId', 'versionNumber'])
  .authorization(allow => [allow.authenticated(), allow.owner()]);
