import { a } from '@aws-amplify/backend';

/**
 * Authentication and user onboarding related models
 */

export const UserOnboarding = a
  .model({
    userId: a.string().required(),
    currentStep: a.string().required(),
    metadata: a.json(),
    lastUpdated: a.datetime(),
    isComplete: a.boolean().default(false),
  })
  .identifier(['userId'])
  .authorization(allow => [allow.owner()]);

export const LoginAttempt = a
  .model({
    email: a.string().required(),
    attemptTime: a.datetime().required(),
    success: a.boolean().required(),
    ipAddress: a.string(),
    userAgent: a.string(),
    lockoutUntil: a.datetime(),
    failedAttempts: a.integer().default(0),
  })
  .identifier(['email'])
  .authorization(allow => [allow.guest(), allow.authenticated()]);

export const LoginHistory = a
  .model({
    email: a.string().required(),
    attemptTime: a.datetime().required(),
    success: a.boolean().required(),
    ipAddress: a.string(),
    userAgent: a.string(),
    sessionId: a.string(),
  })
  .authorization(allow => [allow.guest(), allow.authenticated()]);
