// @ts-nocheck

import type { PostConfirmationTriggerHandler } from 'aws-lambda';
import type { Schema } from '../../data/resource';
import { Amplify } from 'aws-amplify';
import { getAmplifyDataClientConfig } from '@aws-amplify/backend/function/runtime';
import { generateClient } from 'aws-amplify/data';
import { env } from '$amplify/env/postSignUp';

const { resourceConfig, libraryOptions } =
  await getAmplifyDataClientConfig(env);

Amplify.configure(resourceConfig, libraryOptions);

const client = generateClient<Schema>();

/**
 * Randomly selects an item from an array
 */
function getRandomItem<T>(array: T[]): T | null {
  if (!array || array.length === 0) {
    return null;
  }

  const randomIndex = Math.floor(Math.random() * array.length);
  return array[randomIndex];
}

/**
 * Fetch Welon Trust users by state with fallback to any available
 */
async function fetchWelonTrustUsersByState(state: string) {
  try {
    // First, try to find Welon Trust users in the same state
    const { data: stateUsers, errors: stateErrors } =
      await client.models.User.list({
        filter: {
          and: [{ role: { eq: 'WelonTrust' } }, { state: { eq: state } }],
        },
      });

    if (stateErrors) {
      console.error('Errors fetching Welon Trust users by state:', stateErrors);
    }

    // If we found users in the same state, return them
    if (stateUsers && stateUsers.length > 0) {
      console.log(
        `Found ${stateUsers.length} Welon Trust users in state: ${state}`
      );
      return stateUsers;
    }

    // Fallback: fetch any available active Welon Trust users
    console.log(
      `No Welon Trust users found in state ${state}, fetching any available`
    );
    const { data: allUsers, errors: allErrors } = await client.models.User.list(
      {
        filter: {
          and: [{ role: { eq: 'WelonTrust' } }],
        },
      }
    );

    if (allErrors) {
      console.error('Errors fetching all Welon Trust users:', allErrors);
      return [];
    }

    if (!allUsers || allUsers.length === 0) {
      console.warn('No active Welon Trust users found in the system');
      return [];
    }

    console.log(
      `Found ${allUsers.length} active Welon Trust users as fallback`
    );
    return allUsers;
  } catch (error) {
    console.error('Error fetching Welon Trust users by state:', error);
    return [];
  }
}

/**
 * Create a Welon Trust assignment for a user
 */
async function createWelonTrustAssignment(userId: string, welonTrustUser: any) {
  try {
    const { errors } = await client.models.WelonTrustAssignment.create({
      userId,
      welonTrustUserId: welonTrustUser.id,
      welonTrustName: `${welonTrustUser.firstName} ${welonTrustUser.lastName}`,
      welonTrustEmail: welonTrustUser.email,
      welonTrustCognitoId: welonTrustUser.cognitoId,
      assignedAt: new Date().toISOString(),
      assignedBy: 'system-auto-assignment',
      status: 'active',
    });

    if (errors) {
      console.error('Errors creating Welon Trust assignment:', errors);
      throw new Error('Failed to create Welon Trust assignment');
    }

    console.log(
      `Successfully assigned Welon Trust ${welonTrustUser.firstName} ${welonTrustUser.lastName} to user ${userId}`
    );
  } catch (error) {
    console.error('Error creating Welon Trust assignment:', error);
    throw error;
  }
}

export const handler: PostConfirmationTriggerHandler = async event => {
  const userAttributes = event.request.userAttributes;

  const userId = userAttributes.sub;
  const email = userAttributes.email;
  const givenName = userAttributes.given_name;
  const familyName = userAttributes.family_name;
  const phoneNumber = userAttributes.phone_number;
  const birthdate = userAttributes.birthdate;
  const address = userAttributes.address;

  console.log('===> REAL USER ATTRIBUTES', {
    userId,
    email,
    givenName,
    familyName,
    phoneNumber,
    birthdate,
    address,
  });

  try {
    // Create the new user with Member role by default
    const { data: newUser, errors: userErrors } =
      await client.models.User.create({
        firstName: givenName,
        lastName: familyName,
        phoneNumber: phoneNumber,
        birthdate: birthdate,
        state: address,
        cognitoId: userId,
        email,
        role: 'Member',
        status: 'active',
        createdAt: new Date().toISOString(),
      });

    if (userErrors) {
      console.error('Errors creating user:', userErrors);
      throw new Error('Failed to create user');
    }

    if (!newUser) {
      throw new Error('User creation returned null');
    }

    console.log('USER CREATE RESULT: ', newUser);

    // Automatically assign a Welon Trust from the same state
    try {
      console.log(
        `Attempting to assign Welon Trust for user in state: ${address}`
      );

      const availableWelonTrusts = await fetchWelonTrustUsersByState(address);

      if (availableWelonTrusts.length > 0) {
        const selectedWelonTrust = getRandomItem(availableWelonTrusts);

        if (selectedWelonTrust) {
          await createWelonTrustAssignment(newUser.id, selectedWelonTrust);

          // Update the user with the assigned Welon Trust ID
          await client.models.User.update({
            id: newUser.id,
            assignedWelonTrustId: selectedWelonTrust.cognitoId,
          });

          console.log(
            `Successfully assigned Welon Trust ${selectedWelonTrust.firstName} ${selectedWelonTrust.lastName} to new user ${newUser.email}`
          );
        } else {
          console.warn(
            'Random selection returned null despite available Welon Trusts'
          );
        }
      } else {
        console.warn('No Welon Trust users available for assignment');
      }
    } catch (welonTrustError) {
      // Don't fail the entire registration if Welon Trust assignment fails
      console.error(
        'Error assigning Welon Trust (user registration will continue):',
        welonTrustError
      );
    }
  } catch (error) {
    console.error('ERROR CREATING USER', error);
    // Re-throw to fail the registration if user creation fails
    throw error;
  }

  return event;
};
