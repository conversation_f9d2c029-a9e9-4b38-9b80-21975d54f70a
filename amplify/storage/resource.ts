import { defineStorage } from '@aws-amplify/backend';

export const storage = defineStorage({
  name: 'evidenceStorage',
  access: allow => ({
    // Owner-based evidence access - only uploader and admins can access
    'public/evidence/*': [
      allow.groups(['ADMINS']).to(['read', 'write', 'delete']), // <PERSON><PERSON> can access all
      allow.groups(['WELONTRUST']).to(['read', 'write', 'delete']),
    ],
  }),
});
