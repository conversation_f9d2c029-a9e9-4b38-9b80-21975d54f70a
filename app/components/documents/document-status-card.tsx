'use client';

import React from 'react';
import { Card, Card<PERSON>ontent, Card<PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import {
  FileText,
  Calendar,
  User,
  Eye,
  PenTool,
  Download,
  Truck,
  CheckCircle,
  XCircle,
  Clock,
  AlertCircle,
} from 'lucide-react';
import { Document } from '@/types/documents';

interface DocumentStatusCardProps {
  document: Document;
  onReview?: () => void;
  onSign?: () => void;
  onDownload?: () => void;
  onPreview?: () => void;
  className?: string;
}

export function DocumentStatusCard({
  document,
  onReview,
  onSign,
  onDownload,
  onPreview,
  className = '',
}: DocumentStatusCardProps) {
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    });
  };

  const getStatusIcon = (status: Document['status']) => {
    switch (status) {
      case 'draft':
        return <FileText className='h-4 w-4' />;
      case 'ready_for_review':
        return <Eye className='h-4 w-4' />;
      case 'under_review':
        return <Clock className='h-4 w-4' />;
      case 'ready_for_signing':
        return <PenTool className='h-4 w-4' />;
      case 'signed':
        return <CheckCircle className='h-4 w-4' />;
      case 'shipped':
        return <Truck className='h-4 w-4' />;
      case 'received':
        return <CheckCircle className='h-4 w-4' />;
      case 'approved':
        return <CheckCircle className='h-4 w-4' />;
      case 'rejected':
        return <XCircle className='h-4 w-4' />;
      default:
        return <AlertCircle className='h-4 w-4' />;
    }
  };

  const getStatusColor = (status: Document['status']) => {
    switch (status) {
      case 'draft':
        return 'bg-gray-100 text-[var(--custom-gray-dark)]';
      case 'ready_for_review':
        return 'bg-blue-100 text-blue-800';
      case 'under_review':
        return 'bg-yellow-100 text-yellow-800';
      case 'ready_for_signing':
        return 'bg-green-100 text-green-800';
      case 'signed':
        return 'bg-purple-100 text-purple-800';
      case 'shipped':
        return 'bg-indigo-100 text-indigo-800';
      case 'received':
        return 'bg-teal-100 text-teal-800';
      case 'approved':
        return 'bg-emerald-100 text-emerald-800';
      case 'rejected':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-[var(--custom-gray-dark)]';
    }
  };

  const getStatusMessage = (status: Document['status']) => {
    switch (status) {
      case 'draft':
        return 'Document is in draft status';
      case 'ready_for_review':
        return 'Ready for your review';
      case 'under_review':
        return 'Currently under review';
      case 'ready_for_signing':
        return 'Ready for signing';
      case 'signed':
        return 'Document has been signed';
      case 'shipped':
        return 'Shipped to Welon Trust';
      case 'received':
        return 'Received by Welon Trust';
      case 'approved':
        return 'Approved and finalized';
      case 'rejected':
        return 'Rejected - needs correction';
      default:
        return 'Status unknown';
    }
  };

  const getAvailableActions = () => {
    const actions = [];

    // Preview is always available
    if (onPreview) {
      actions.push(
        <Button
          key='preview'
          variant='outline'
          size='sm'
          onClick={onPreview}
          className='flex items-center gap-2'
        >
          <Eye className='h-4 w-4' />
          Preview
        </Button>
      );
    }

    // Download is available for most statuses
    if (onDownload && document.status !== 'draft') {
      actions.push(
        <Button
          key='download'
          variant='outline'
          size='sm'
          onClick={onDownload}
          className='flex items-center gap-2'
        >
          <Download className='h-4 w-4' />
          Download
        </Button>
      );
    }

    // Review action
    if (
      onReview &&
      (document.status === 'ready_for_review' || document.status === 'draft')
    ) {
      actions.push(
        <Button
          key='review'
          variant='default'
          size='sm'
          onClick={onReview}
          className='flex items-center gap-2'
        >
          <Eye className='h-4 w-4' />
          Review
        </Button>
      );
    }

    // Sign action
    if (onSign && document.status === 'ready_for_signing') {
      actions.push(
        <Button
          key='sign'
          variant='default'
          size='sm'
          onClick={onSign}
          className='flex items-center gap-2 bg-green-600 hover:bg-green-700'
        >
          <PenTool className='h-4 w-4' />
          Sign
        </Button>
      );
    }

    return actions;
  };

  return (
    <Card className={`w-full ${className}`}>
      <CardHeader className='pb-3'>
        <div className='flex items-start justify-between'>
          <div className='flex items-start gap-3'>
            <div className='p-2 bg-blue-50 rounded-lg'>
              <FileText className='h-5 w-5 text-blue-600' />
            </div>
            <div>
              <CardTitle className='text-lg'>{document.title}</CardTitle>
              <div className='flex items-center gap-4 mt-1 text-sm text-muted-foreground'>
                <div className='flex items-center gap-1'>
                  <User className='h-4 w-4' />
                  <span>{document.type}</span>
                </div>
                <div className='flex items-center gap-1'>
                  <Calendar className='h-4 w-4' />
                  <span>v{document.version}</span>
                </div>
              </div>
            </div>
          </div>
          <Badge className={getStatusColor(document.status)}>
            <div className='flex items-center gap-1'>
              {getStatusIcon(document.status)}
              {document.status.replace('_', ' ').toUpperCase()}
            </div>
          </Badge>
        </div>
      </CardHeader>
      <CardContent className='pt-0'>
        <div className='space-y-4'>
          {/* Status Message */}
          <div className='flex items-center gap-2 text-sm text-muted-foreground'>
            {getStatusIcon(document.status)}
            <span>{getStatusMessage(document.status)}</span>
          </div>

          {/* Additional Status Info */}
          {document.status === 'shipped' && document.trackingNumber && (
            <div className='bg-blue-50 border border-blue-200 rounded-lg p-3'>
              <div className='flex items-center gap-2 text-sm'>
                <Truck className='h-4 w-4 text-blue-600' />
                <span className='font-medium'>Tracking Number:</span>
                <span className='font-mono'>{document.trackingNumber}</span>
              </div>
            </div>
          )}

          {document.status === 'rejected' && document.rejectionReason && (
            <div className='bg-red-50 border border-red-200 rounded-lg p-3'>
              <div className='flex items-start gap-2 text-sm'>
                <XCircle className='h-4 w-4 text-red-600 mt-0.5' />
                <div>
                  <span className='font-medium text-red-800'>
                    Rejection Reason:
                  </span>
                  <p className='text-red-700 mt-1'>
                    {document.rejectionReason}
                  </p>
                </div>
              </div>
            </div>
          )}

          {/* Document Metadata */}
          <div className='grid grid-cols-2 gap-4 text-sm'>
            <div>
              <span className='text-muted-foreground'>Created:</span>
              <div className='font-medium'>
                {formatDate(document.dateCreated)}
              </div>
            </div>
            <div>
              <span className='text-muted-foreground'>Last Modified:</span>
              <div className='font-medium'>
                {formatDate(document.lastModified)}
              </div>
            </div>
          </div>

          {/* Actions */}
          <div className='flex items-center gap-2 pt-2 border-t'>
            {getAvailableActions()}
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
