'use client';

import React, { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import {
  ZoomIn,
  ZoomOut,
  RotateCw,
  Download,
  FileText,
  Calendar,
  User,
} from 'lucide-react';
import { Document } from '@/types/documents';

interface PDFViewerProps {
  document: Document;
  showDraftWatermark?: boolean;
  onDownload?: () => void;
  isDownloading?: boolean;
  className?: string;
}

export function PDFViewer({
  document,
  showDraftWatermark = true,
  onDownload,
  isDownloading = false,
  className = '',
}: PDFViewerProps) {
  const [zoom, setZoom] = useState(100);
  const [rotation, setRotation] = useState(0);

  const handleZoomIn = () => setZoom(prev => Math.min(prev + 25, 200));
  const handleZoomOut = () => setZoom(prev => Math.max(prev - 25, 50));
  const handleRotate = () => setRotation(prev => (prev + 90) % 360);

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  const getStatusColor = (status: Document['status']) => {
    switch (status) {
      case 'draft':
        return 'bg-gray-100 text-[var(--custom-gray-dark)]';
      case 'ready_for_review':
        return 'bg-blue-100 text-blue-800';
      case 'under_review':
        return 'bg-yellow-100 text-yellow-800';
      case 'ready_for_signing':
        return 'bg-green-100 text-green-800';
      case 'signed':
        return 'bg-purple-100 text-purple-800';
      case 'approved':
        return 'bg-emerald-100 text-emerald-800';
      case 'rejected':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-[var(--custom-gray-dark)]';
    }
  };

  const renderDocumentContent = () => {
    return (
      <div className='bg-background border rounded-lg shadow-inner p-8 min-h-[800px] relative overflow-hidden'>
        {/* Draft Watermark */}
        {showDraftWatermark && (
          <div className='absolute inset-0 flex items-center justify-center opacity-10 rotate-45 text-red-500 text-8xl font-bold pointer-events-none z-10'>
            DRAFT
          </div>
        )}

        <div
          className='transition-transform duration-200 w-full relative z-20'
          style={{
            transform: `scale(${zoom / 100}) rotate(${rotation}deg)`,
            transformOrigin: 'center center',
          }}
        >
          {/* Real document content */}
          <div className='max-w-2xl mx-auto space-y-6 text-[var(--custom-gray-dark)]'>
            <div className='text-center border-b pb-4'>
              <h1 className='text-2xl font-bold text-[var(--custom-gray-dark)]'>
                {document.title}
              </h1>
              <p className='text-sm text-[var(--custom-gray-medium)] mt-2'>
                Document Type: {document.type}
              </p>
              <p className='text-xs text-[var(--custom-gray-medium)] mt-1'>
                Version {document.version} • Created{' '}
                {formatDate(document.dateCreated)}
              </p>
            </div>

            {/* Real template content */}
            {document.content ? (
              <div
                className='prose max-w-none text-sm leading-relaxed'
                dangerouslySetInnerHTML={{ __html: document.content }}
              />
            ) : (
              <div className='text-center text-[var(--custom-gray-medium)] py-8'>
                <p className='text-lg mb-2'>No content available</p>
                <p className='text-sm'>
                  This document does not have any content yet.
                </p>
              </div>
            )}
          </div>
        </div>
      </div>
    );
  };

  return (
    <Card className={`w-full ${className}`}>
      <CardHeader className='pb-4'>
        <div className='flex items-center justify-between'>
          <div className='flex items-center gap-3'>
            <FileText className='h-6 w-6 text-blue-600' />
            <div>
              <CardTitle className='text-xl'>{document.title}</CardTitle>
              <div className='flex items-center gap-4 mt-1 text-sm text-muted-foreground'>
                <div className='flex items-center gap-1'>
                  <User className='h-4 w-4' />
                  <span>{document.type}</span>
                </div>
                <div className='flex items-center gap-1'>
                  <Calendar className='h-4 w-4' />
                  <span>Updated {formatDate(document.lastModified)}</span>
                </div>
              </div>
            </div>
          </div>
          <Badge className={getStatusColor(document.status)}>
            {document.status.replace('_', ' ').toUpperCase()}
          </Badge>
        </div>

        {/* Viewer Controls */}
        <div className='flex items-center gap-2 pt-4 border-t'>
          <Button
            variant='outline'
            size='sm'
            onClick={handleZoomOut}
            disabled={zoom <= 50}
          >
            <ZoomOut className='h-4 w-4' />
          </Button>
          <span className='text-sm font-medium min-w-[60px] text-center'>
            {zoom}%
          </span>
          <Button
            variant='outline'
            size='sm'
            onClick={handleZoomIn}
            disabled={zoom >= 200}
          >
            <ZoomIn className='h-4 w-4' />
          </Button>
          <Button variant='outline' size='sm' onClick={handleRotate}>
            <RotateCw className='h-4 w-4' />
          </Button>
          {onDownload && (
            <Button
              variant='outline'
              size='sm'
              onClick={onDownload}
              disabled={isDownloading}
              className='ml-auto'
            >
              <Download className='h-4 w-4 mr-2' />
              {isDownloading ? 'Signing & Downloading...' : 'Download PDF'}
            </Button>
          )}
        </div>
      </CardHeader>
      <CardContent className='p-0'>{renderDocumentContent()}</CardContent>
    </Card>
  );
}
