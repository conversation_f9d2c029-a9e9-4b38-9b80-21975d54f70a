'use client';

import React, { useState, useEffect } from 'react';
import { useInterviewNew } from './interview-new-context';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { Checkbox } from '@/components/ui/checkbox';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Label } from '@/components/ui/label';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { ChevronLeft, ChevronRight, Info } from 'lucide-react';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/components/ui/tooltip';

interface QuestionTooltipProps {
  content: string;
}

const QuestionTooltip: React.FC<QuestionTooltipProps> = ({ content }) => {
  return (
    <TooltipProvider>
      <Tooltip>
        <TooltipTrigger asChild>
          <Info className='w-4 h-4 ml-2 text-gray-500 cursor-help' />
        </TooltipTrigger>
        <TooltipContent>
          <p className='max-w-xs'>{content}</p>
        </TooltipContent>
      </Tooltip>
    </TooltipProvider>
  );
};

// Helper function to parse options from different formats
const parseOptions = (
  options?: string[] | any[]
): Array<{
  id: string;
  label: string;
  value: string;
  nextQuestionId?: string;
}> => {
  if (!options || !Array.isArray(options)) return [];

  return options.map((option, index) => {
    // Handle JSON string options (from the database)
    if (typeof option === 'string') {
      try {
        // Try to parse as JSON first
        const parsed = JSON.parse(option);
        if (typeof parsed === 'object' && parsed !== null) {
          return {
            id: parsed.id || `option_${index}`,
            label: parsed.label || parsed.value || String(parsed),
            value: parsed.value || parsed.label || String(parsed),
            nextQuestionId: parsed.nextQuestionId,
          };
        }
      } catch (e) {
        // If JSON parsing fails, treat as simple string
      }

      // Simple string option
      return {
        id: `option_${index}`,
        label: option,
        value: option,
      };
    } else if (typeof option === 'object' && option !== null) {
      // Already parsed object
      return {
        id: option.id || `option_${index}`,
        label: option.label || option.value || String(option),
        value: option.value || option.label || String(option),
        nextQuestionId: option.nextQuestionId,
      };
    } else {
      return {
        id: `option_${index}`,
        label: String(option),
        value: String(option),
      };
    }
  });
};

const QuestionInput: React.FC<{
  questionId: string;
  type: string;
  options?: string[] | any[];
  value: string;
  onChange: (value: string) => void;
  onOptionSelect?: (option: {
    id: string;
    label: string;
    value: string;
    nextQuestionId?: string;
  }) => void;
  placeholder?: string;
}> = ({
  questionId,
  type,
  options,
  value,
  onChange,
  onOptionSelect,
  placeholder,
}) => {
  const parsedOptions = parseOptions(options);
  switch (type) {
    case 'text':
      return (
        <Input
          id={questionId}
          value={value}
          onChange={e => onChange(e.target.value)}
          placeholder={placeholder || 'Enter your answer...'}
          className='w-full'
        />
      );

    case 'textarea':
      return (
        <Textarea
          id={questionId}
          value={value}
          onChange={e => onChange(e.target.value)}
          placeholder={placeholder || 'Enter your answer...'}
          className='w-full min-h-[100px]'
        />
      );

    case 'radio':
      const handleRadioChange = (selectedValue: string) => {
        onChange(selectedValue);
        // Find the selected option and trigger conditional logic
        const selectedOption = parsedOptions.find(
          opt => opt.value === selectedValue
        );
        if (selectedOption && onOptionSelect) {
          onOptionSelect(selectedOption);
        }
      };

      return (
        <RadioGroup value={value} onValueChange={handleRadioChange}>
          {parsedOptions.map(option => (
            <div key={option.id} className='flex items-center space-x-2'>
              <RadioGroupItem
                value={option.value}
                id={`${questionId}-${option.id}`}
              />
              <Label htmlFor={`${questionId}-${option.id}`}>
                {option.label}
              </Label>
            </div>
          ))}
        </RadioGroup>
      );

    case 'select':
      const handleSelectChange = (selectedValue: string) => {
        onChange(selectedValue);
        // Find the selected option and trigger conditional logic
        const selectedOption = parsedOptions.find(
          opt => opt.value === selectedValue
        );
        if (selectedOption && onOptionSelect) {
          onOptionSelect(selectedOption);
        }
      };

      return (
        <Select value={value} onValueChange={handleSelectChange}>
          <SelectTrigger className='w-full'>
            <SelectValue placeholder='Select an option...' />
          </SelectTrigger>
          <SelectContent>
            {parsedOptions.map(option => (
              <SelectItem key={option.id} value={option.value}>
                {option.label}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      );

    case 'checkbox':
      const selectedValues = value ? value.split(',').filter(Boolean) : [];

      const handleCheckboxChange = (optionValue: string, checked: boolean) => {
        let newValues: string[];
        if (checked) {
          newValues = [...selectedValues, optionValue];
        } else {
          newValues = selectedValues.filter(v => v !== optionValue);
        }
        onChange(newValues.join(','));

        // Trigger conditional logic for checkbox selections
        if (checked && onOptionSelect) {
          const selectedOption = parsedOptions.find(
            opt => opt.value === optionValue
          );
          if (selectedOption) {
            onOptionSelect(selectedOption);
          }
        }
      };

      return (
        <div className='space-y-2'>
          {parsedOptions.map(option => (
            <div key={option.id} className='flex items-center space-x-2'>
              <Checkbox
                id={`${questionId}-${option.id}`}
                checked={selectedValues.includes(option.value)}
                onCheckedChange={checked =>
                  handleCheckboxChange(option.value, checked as boolean)
                }
              />
              <Label htmlFor={`${questionId}-${option.id}`}>
                {option.label}
              </Label>
            </div>
          ))}
        </div>
      );

    default:
      return (
        <div className='text-red-500'>Unsupported question type: {type}</div>
      );
  }
};

export const QuestionCard: React.FC = () => {
  const {
    questions,
    currentQuestionIndex,
    currentAnswer,
    isLoading,
    error,
    setCurrentAnswer,
    saveAnswer,
    saveAnswerAndNavigate,
    goToNextQuestion,
    goToPreviousQuestion,
    goToQuestionById,
    getAnswerForQuestion,
    getProgress,
  } = useInterviewNew();

  console.log('===> QUESTIONS', questions);

  const [isSubmitting, setIsSubmitting] = useState(false);
  const [localAnswer, setLocalAnswer] = useState<string>('');
  const [selectedOption, setSelectedOption] = useState<{
    id: string;
    label: string;
    value: string;
    nextQuestionId?: string;
  } | null>(null);

  // Get current question
  const currentQuestion = questions[currentQuestionIndex];
  const parsedOptions = parseOptions(currentQuestion?.options);

  // Debug logging
  useEffect(() => {
    if (currentQuestion) {
      console.log('=== CURRENT QUESTION ===');
      console.log('Question:', currentQuestion.text);
      console.log('Question ID:', currentQuestion.questionId);
      console.log('Question Order:', currentQuestion.order);
      console.log('Raw options:', currentQuestion.options);
      console.log('Parsed options:', parsedOptions);
      console.log('========================');
    }
  }, [currentQuestion, parsedOptions]);

  // Update local answer when question changes or current answer changes
  useEffect(() => {
    setLocalAnswer(currentAnswer);
    setSelectedOption(null); // Reset selected option when question changes
  }, [currentAnswer, currentQuestionIndex]);

  // Handle form field changes (no database save)
  const handleChange = (value: string) => {
    setLocalAnswer(value);
    setCurrentAnswer(value);
    setSelectedOption(null); // Reset selected option for text inputs
  };

  // Handle option selection for conditional logic
  const handleOptionSelect = (option: {
    id: string;
    label: string;
    value: string;
    nextQuestionId?: string;
  }) => {
    console.log('Option selected:', option);
    setSelectedOption(option);
    setLocalAnswer(option.value);
    setCurrentAnswer(option.value);
  };

  // Handle next button click with database save and conditional navigation
  const handleNext = async () => {
    if (!currentQuestion) return;

    setIsSubmitting(true);
    try {
      // Find the option that matches the current answer to get conditional logic
      let nextQuestionId: string | undefined;

      if (parsedOptions.length > 0) {
        const matchingOption = parsedOptions.find(
          opt => opt.value === localAnswer
        );
        nextQuestionId = matchingOption?.nextQuestionId;

        console.log('Current answer:', localAnswer);
        console.log('Matching option:', matchingOption);
        console.log('Next question ID:', nextQuestionId);
      }

      // Save current answer and navigate based on conditional logic
      await saveAnswerAndNavigate(
        currentQuestion.questionId,
        localAnswer,
        nextQuestionId
      );
    } catch (error) {
      console.error('Error saving response:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  // Handle previous button click
  const handlePrevious = () => {
    goToPreviousQuestion();
  };

  if (isLoading) {
    return (
      <Card className='max-w-2xl mx-auto'>
        <CardContent className='p-6'>
          <div className='animate-pulse space-y-4'>
            <div className='h-4 bg-gray-200 rounded w-1/4'></div>
            <div className='h-8 bg-gray-200 rounded w-3/4'></div>
            <div className='h-20 bg-gray-200 rounded'></div>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Card className='max-w-2xl mx-auto border-red-200'>
        <CardContent className='p-6'>
          <div className='text-red-600 text-center'>
            <p className='font-semibold'>Error loading interview</p>
            <p className='text-sm mt-1'>{error}</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (!currentQuestion) {
    return (
      <Card className='max-w-2xl mx-auto'>
        <CardContent className='p-6'>
          <div className='text-center text-gray-500'>
            <p>No questions available</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  const progress = getProgress();
  const isFirstQuestion = currentQuestionIndex === 0;
  const isLastQuestion = currentQuestionIndex === questions.length - 1;

  return (
    <Card className='max-w-2xl mx-auto shadow-lg'>
      <CardHeader className='pb-4'>
        <div className='flex justify-between items-center mb-2'>
          <span className='text-sm text-gray-500'>
            Question {currentQuestionIndex + 1} of {questions.length}
          </span>
          <span className='text-sm text-gray-500'>{progress}% Complete</span>
        </div>

        {/* Progress bar */}
        <div className='w-full bg-gray-200 rounded-full h-2 mb-4'>
          <div
            className='bg-blue-600 h-2 rounded-full transition-all duration-300'
            style={{ width: `${progress}%` }}
          ></div>
        </div>

        <CardTitle className='text-xl font-bold text-gray-900 flex items-center'>
          {currentQuestion.text}
          {/* Show tooltip if question has conditional logic */}
          {currentQuestion.conditionalLogic &&
            currentQuestion.conditionalLogic.length > 0 && (
              <QuestionTooltip content='This question may show additional questions based on your answer' />
            )}
          {/* Show tooltip if any options have nextQuestionId (branching logic) */}
          {parsedOptions.some(opt => opt.nextQuestionId) && (
            <QuestionTooltip content='Some answers will lead to different follow-up questions' />
          )}
        </CardTitle>
      </CardHeader>

      <CardContent className='space-y-6'>
        {/* Question input */}
        <div className='space-y-2'>
          <QuestionInput
            questionId={currentQuestion.questionId}
            type={currentQuestion.type}
            options={currentQuestion.options}
            value={localAnswer}
            onChange={handleChange}
            onOptionSelect={handleOptionSelect}
            placeholder={`Enter your ${currentQuestion.type === 'text' ? 'answer' : 'selection'}...`}
          />
        </div>

        {/* Navigation buttons */}
        <div className='flex justify-between pt-4'>
          <Button
            variant='outline'
            onClick={handlePrevious}
            disabled={isFirstQuestion || isSubmitting}
            className='flex items-center gap-2'
          >
            <ChevronLeft className='w-4 h-4' />
            Previous
          </Button>

          <Button
            onClick={handleNext}
            disabled={isSubmitting}
            className='flex items-center gap-2'
          >
            {isSubmitting ? (
              'Saving...'
            ) : isLastQuestion ? (
              'Complete Interview'
            ) : (
              <>
                Next
                <ChevronRight className='w-4 h-4' />
              </>
            )}
          </Button>
        </div>

        {/* Required field indicator */}
        {currentQuestion.isRequired && (
          <p className='text-xs text-gray-500 mt-2'>* This field is required</p>
        )}
      </CardContent>
    </Card>
  );
};
