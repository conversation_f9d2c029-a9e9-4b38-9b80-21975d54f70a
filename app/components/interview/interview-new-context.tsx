'use client';

import React, { createContext, useContext, useState, useEffect } from 'react';
import {
  getActiveInterviews,
  getInterviewById,
  getUserInterviewProgress,
  saveUserAnswer,
  resetUserInterviewProgress,
  InterviewQuestion,
  InterviewWithVersion,
  UserInterviewProgress,
  UserAnswer,
} from '@/lib/api/interview-new-user';

interface InterviewContextType {
  // Interview data
  interviews: InterviewWithVersion[];
  currentInterview: InterviewWithVersion | null;
  questions: InterviewQuestion[];

  // User progress
  userProgress: UserInterviewProgress | null;
  currentQuestionIndex: number;
  currentAnswer: string;

  // State
  isLoading: boolean;
  isComplete: boolean;
  error: string | null;

  // Actions
  selectInterview: (interviewId: string) => Promise<void>;
  setCurrentAnswer: (answer: string) => void;
  saveAnswer: (questionId: string, answer: string) => Promise<void>;
  saveAnswerAndNavigate: (
    questionId: string,
    answer: string,
    nextQuestionId?: string
  ) => Promise<void>;
  goToNextQuestion: () => void;
  goToPreviousQuestion: () => void;
  goToQuestion: (index: number) => void;
  goToQuestionById: (questionId: string) => void;
  completeInterview: () => Promise<void>;
  resetInterview: () => Promise<void>;

  // Helpers
  getAnswerForQuestion: (questionId: string) => string | undefined;
  isQuestionAnswered: (questionId: string) => boolean;
  getProgress: () => number;
  getVisibleQuestions: () => InterviewQuestion[];
  shouldShowQuestion: (questionId: string) => boolean;
}

const InterviewContext = createContext<InterviewContextType | undefined>(
  undefined
);

export const useInterviewNew = () => {
  const context = useContext(InterviewContext);
  if (context === undefined) {
    throw new Error(
      'useInterviewNew must be used within an InterviewNewProvider'
    );
  }
  return context;
};

interface InterviewNewProviderProps {
  children: React.ReactNode;
  defaultInterviewId?: string;
  defaultInterviewSelector?: 'first' | 'mostQuestions' | 'leastQuestions';
}

export const InterviewNewProvider: React.FC<InterviewNewProviderProps> = ({
  children,
  defaultInterviewId,
  defaultInterviewSelector = 'first',
}) => {
  // State
  const [interviews, setInterviews] = useState<InterviewWithVersion[]>([]);
  const [currentInterview, setCurrentInterview] =
    useState<InterviewWithVersion | null>(null);
  const [userProgress, setUserProgress] =
    useState<UserInterviewProgress | null>(null);
  const [currentQuestionIndex, setCurrentQuestionIndex] = useState(0);
  const [currentAnswer, setCurrentAnswer] = useState<string>('');
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Derived state
  const questions = currentInterview?.latestVersion.questions || [];
  const isComplete = userProgress?.isCompleted || false;

  // Load interviews on mount
  useEffect(() => {
    loadInterviews();
  }, []);

  // Load specific interview if defaultInterviewId is provided
  useEffect(() => {
    if (defaultInterviewId && interviews.length > 0) {
      selectInterview(defaultInterviewId);
    } else if (interviews.length > 0 && !currentInterview) {
      // Select interview based on strategy
      let selectedInterview = interviews[0]; // Default fallback

      switch (defaultInterviewSelector) {
        case 'mostQuestions':
          selectedInterview = interviews.reduce((prev, current) =>
            current.latestVersion.questions.length >
            prev.latestVersion.questions.length
              ? current
              : prev
          );
          break;
        case 'leastQuestions':
          selectedInterview = interviews.reduce((prev, current) =>
            current.latestVersion.questions.length <
            prev.latestVersion.questions.length
              ? current
              : prev
          );
          break;
        case 'first':
        default:
          selectedInterview = interviews[0];
          break;
      }

      selectInterview(selectedInterview.id);
    }
  }, [defaultInterviewId, interviews, defaultInterviewSelector]);

  // Load user progress when interview changes
  useEffect(() => {
    if (currentInterview) {
      loadUserProgress();
    }
  }, [currentInterview]);

  // Update current answer when question changes
  useEffect(() => {
    if (questions.length > 0 && currentQuestionIndex < questions.length) {
      const currentQuestion = questions[currentQuestionIndex];
      const savedAnswer = getAnswerForQuestion(currentQuestion.questionId);
      setCurrentAnswer(savedAnswer || '');
    }
  }, [currentQuestionIndex, questions, userProgress]);

  const loadInterviews = async () => {
    try {
      setIsLoading(true);
      setError(null);
      const fetchedInterviews = await getActiveInterviews();
      setInterviews(fetchedInterviews);
    } catch (err) {
      setError('Failed to load interviews');
      console.error('Error loading interviews:', err);
    } finally {
      setIsLoading(false);
    }
  };

  const selectInterview = async (interviewId: string) => {
    try {
      setIsLoading(true);
      setError(null);

      const interview = await getInterviewById(interviewId);
      if (!interview) {
        throw new Error('Interview not found');
      }

      setCurrentInterview(interview);
      setCurrentQuestionIndex(0);
    } catch (err) {
      setError('Failed to load interview');
      console.error('Error selecting interview:', err);
    } finally {
      setIsLoading(false);
    }
  };

  const loadUserProgress = async () => {
    if (!currentInterview) return;

    try {
      const progress = await getUserInterviewProgress(
        currentInterview.latestVersion.id
      );
      setUserProgress(progress);

      // Set current question index based on progress
      if (progress && progress.currentQuestionId) {
        const questionIndex = questions.findIndex(
          q => q.questionId === progress.currentQuestionId
        );
        if (questionIndex >= 0) {
          setCurrentQuestionIndex(questionIndex);
        }
      }
    } catch (err) {
      console.error('Error loading user progress:', err);
    }
  };

  const saveAnswer = async (questionId: string, answer: string) => {
    if (!currentInterview) return;

    try {
      const success = await saveUserAnswer(
        currentInterview.latestVersion.id,
        questionId,
        answer,
        false
      );

      if (success) {
        // Reload user progress to get updated state
        await loadUserProgress();
      } else {
        throw new Error('Failed to save answer');
      }
    } catch (err) {
      setError('Failed to save answer');
      console.error('Error saving answer:', err);
      throw err;
    }
  };

  const saveAnswerAndNavigate = async (
    questionId: string,
    answer: string,
    nextQuestionId?: string
  ) => {
    if (!currentInterview) return;

    try {
      console.log('=== SAVE AND NAVIGATE ===');
      console.log('Question ID:', questionId);
      console.log('Answer:', answer);
      console.log('Next Question ID:', nextQuestionId);
      console.log('Current Question Index:', currentQuestionIndex);

      // Save the answer first
      await saveAnswer(questionId, answer);

      // Then handle navigation
      if (nextQuestionId) {
        // Navigate to conditional question
        console.log('Navigating to conditional question:', nextQuestionId);
        const targetIndex = questions.findIndex(
          q => q.questionId === nextQuestionId
        );
        console.log('Target question index:', targetIndex);
        if (targetIndex >= 0) {
          console.log('Target question:', questions[targetIndex]);
        }
        goToQuestionById(nextQuestionId);
      } else {
        // Navigate to next question in sequence
        console.log('Navigating to next question in sequence');
        goToNextQuestion();
      }
    } catch (err) {
      console.error('Error saving answer and navigating:', err);
      throw err;
    }
  };

  const goToNextQuestion = () => {
    if (currentQuestionIndex < questions.length - 1) {
      setCurrentQuestionIndex(currentQuestionIndex + 1);
    }
  };

  const goToPreviousQuestion = () => {
    if (currentQuestionIndex > 0) {
      setCurrentQuestionIndex(currentQuestionIndex - 1);
    }
  };

  const goToQuestion = (index: number) => {
    if (index >= 0 && index < questions.length) {
      setCurrentQuestionIndex(index);
    }
  };

  const goToQuestionById = (questionId: string) => {
    console.log('=== GO TO QUESTION BY ID ===');
    console.log('Looking for question ID:', questionId);
    console.log(
      'Available questions:',
      questions.map(q => ({ id: q.questionId, text: q.text, order: q.order }))
    );

    const questionIndex = questions.findIndex(q => q.questionId === questionId);
    console.log('Found question at index:', questionIndex);

    if (questionIndex >= 0) {
      console.log('Setting current question index to:', questionIndex);
      console.log('Target question:', questions[questionIndex]);
      setCurrentQuestionIndex(questionIndex);
    } else {
      console.error('Question not found with ID:', questionId);
    }
  };

  const completeInterview = async () => {
    if (!currentInterview || questions.length === 0) return;

    try {
      const lastQuestion = questions[questions.length - 1];
      const success = await saveUserAnswer(
        currentInterview.latestVersion.id,
        lastQuestion.questionId,
        currentAnswer,
        true // Mark as completed
      );

      if (success) {
        await loadUserProgress();
      } else {
        throw new Error('Failed to complete interview');
      }
    } catch (err) {
      setError('Failed to complete interview');
      console.error('Error completing interview:', err);
      throw err;
    }
  };

  const resetInterview = async () => {
    if (!currentInterview) return;

    try {
      const success = await resetUserInterviewProgress(
        currentInterview.latestVersion.id
      );

      if (success) {
        setUserProgress(null);
        setCurrentQuestionIndex(0);
        setCurrentAnswer('');
      } else {
        throw new Error('Failed to reset interview');
      }
    } catch (err) {
      setError('Failed to reset interview');
      console.error('Error resetting interview:', err);
      throw err;
    }
  };

  const getAnswerForQuestion = (questionId: string): string | undefined => {
    if (!userProgress?.answers) return undefined;

    const answer = userProgress.answers.find(a => a.questionId === questionId);
    return answer?.answer;
  };

  const isQuestionAnswered = (questionId: string): boolean => {
    return getAnswerForQuestion(questionId) !== undefined;
  };

  const getProgress = (): number => {
    if (questions.length === 0) return 0;

    const answeredCount = userProgress?.answers?.length || 0;
    return Math.round((answeredCount / questions.length) * 100);
  };

  // Helper to check if a question should be shown based on conditional logic
  const shouldShowQuestion = (questionId: string): boolean => {
    if (!userProgress?.answers) return true; // Show all questions if no answers yet

    // Find all questions that have this questionId as a nextQuestionId in their options
    const conditionalParents = questions.filter(q => {
      if (!q.options || !Array.isArray(q.options)) return false;

      return q.options.some(option => {
        try {
          if (typeof option === 'string') {
            const parsed = JSON.parse(option);
            return parsed.nextQuestionId === questionId;
          } else if (typeof option === 'object' && option !== null) {
            return (option as any).nextQuestionId === questionId;
          }
        } catch (e) {
          // Ignore parsing errors
        }
        return false;
      });
    });

    // If no conditional parents, always show the question
    if (conditionalParents.length === 0) return true;

    // Check if any parent question has an answer that leads to this question
    return conditionalParents.some(parentQuestion => {
      const parentAnswer = getAnswerForQuestion(parentQuestion.questionId);
      if (!parentAnswer) return false;

      // Check if the parent's answer matches an option that leads to this question
      return parentQuestion.options?.some(option => {
        try {
          let optionData: any;
          if (typeof option === 'string') {
            optionData = JSON.parse(option);
          } else {
            optionData = option as any;
          }

          return (
            optionData.value === parentAnswer &&
            optionData.nextQuestionId === questionId
          );
        } catch (e) {
          return false;
        }
      });
    });
  };

  // Get only the questions that should be visible based on conditional logic
  const getVisibleQuestions = (): InterviewQuestion[] => {
    return questions.filter(question =>
      shouldShowQuestion(question.questionId)
    );
  };

  const contextValue: InterviewContextType = {
    // Interview data
    interviews,
    currentInterview,
    questions,

    // User progress
    userProgress,
    currentQuestionIndex,
    currentAnswer,

    // State
    isLoading,
    isComplete,
    error,

    // Actions
    selectInterview,
    setCurrentAnswer,
    saveAnswer,
    saveAnswerAndNavigate,
    goToNextQuestion,
    goToPreviousQuestion,
    goToQuestion,
    goToQuestionById,
    completeInterview,
    resetInterview,

    // Helpers
    getAnswerForQuestion,
    isQuestionAnswered,
    getProgress,
    getVisibleQuestions,
    shouldShowQuestion,
  };

  return (
    <InterviewContext.Provider value={contextValue}>
      {children}
    </InterviewContext.Provider>
  );
};
