'use client';

import * as React from 'react';
import { ThemeProvider as NextThemesProvider } from 'next-themes';
import { RoleProvider } from '@/lib/roles/role-context';
import { NotificationProvider } from '@/lib/notifications/notification-context';
import { LinkedAccountProvider } from '@/lib/contexts/linked-account-context';

export function Providers({ children }: { children: React.ReactNode }) {
  return (
    <NextThemesProvider
      attribute='class'
      defaultTheme='light'
      enableSystem
      disableTransitionOnChange
      enableColorScheme
    >
      <RoleProvider>
        <NotificationProvider>
          <LinkedAccountProvider>{children}</LinkedAccountProvider>
        </NotificationProvider>
      </RoleProvider>
    </NextThemesProvider>
  );
}
