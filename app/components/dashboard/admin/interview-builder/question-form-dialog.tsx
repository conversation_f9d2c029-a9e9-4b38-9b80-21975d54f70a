'use client';

import React, { useState, useEffect } from 'react';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Checkbox } from '@/components/ui/checkbox';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Badge } from '@/components/ui/badge';
import {
  Save,
  AlertTriangle,
  Plus,
  Trash2,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON>ting<PERSON>,
  List,
  ArrowRight,
} from 'lucide-react';
import {
  InterviewQuestion,
  CreateQuestionRequest,
  UpdateQuestionRequest,
  QuestionType,
  QuestionCategory,
  QuestionOption,
  ValidationRule,
  ConditionalLogic,
} from '@/types/interview-builder';
import {
  createQuestion,
  updateQuestion,
  validateQuestionData,
  getTemplateVariables,
} from '@/lib/api/interview-builder';

interface QuestionFormDialogProps {
  interviewId: string;
  versionId: string;
  question: InterviewQuestion | null;
  isOpen: boolean;
  onClose: () => void;
  onSave: () => void;
  refetch: () => void;
}

const QUESTION_TYPES: {
  value: QuestionType;
  label: string;
  description: string;
}[] = [
  { value: 'text', label: 'Text Input', description: 'Single line text input' },
  // {
  //   value: 'email',
  //   label: 'Email',
  //   description: 'Email address input with validation',
  // },
  // {
  //   value: 'phone',
  //   label: 'Phone',
  //   description: 'Phone number input with formatting',
  // },
  // { value: 'number', label: 'Number', description: 'Numeric input' },
  // { value: 'date', label: 'Date', description: 'Date picker' },
  {
    value: 'radio',
    label: 'Radio Buttons',
    description: 'Single choice from options',
  },
  {
    value: 'select',
    label: 'Dropdown',
    description: 'Single choice from dropdown',
  },
  {
    value: 'checkbox',
    label: 'Checkboxes',
    description: 'Multiple choice options',
  },
];

const QUESTION_CATEGORIES: { value: QuestionCategory; label: string }[] = [
  { value: 'personal', label: 'Personal Information' },
  { value: 'financial', label: 'Financial Assets' },
  { value: 'estate', label: 'Estate Planning' },
  { value: 'emergency', label: 'Emergency Contacts' },
  { value: 'medical', label: 'Medical Information' },
];

export function QuestionFormDialog({
  interviewId,
  versionId,
  question,
  isOpen,
  onClose,
  onSave,
  refetch,
}: QuestionFormDialogProps) {
  const [formData, setFormData] = useState({
    text: '',
    type: 'text' as QuestionType,
    category: 'personal' as QuestionCategory,
    required: false,
    helpText: '',
    placeholder: '',
    options: [] as QuestionOption[],
    validation: [] as ValidationRule[],
    conditionalLogic: [] as ConditionalLogic[],
    defaultNextQuestionId: '',
    templateMapping: '',
    order: 1,
  });

  const [errors, setErrors] = useState<Record<string, string>>({});
  const [saving, setSaving] = useState(false);
  const [activeTab, setActiveTab] = useState('basic');

  const isEditing = !!question;

  useEffect(() => {
    if (question) {
      setFormData({
        text: question.text,
        type: question.type,
        category: question.category,
        required: question.required,
        helpText: question.helpText || '',
        placeholder: question.placeholder || '',
        options: question.options || [],
        validation: question.validation || [],
        conditionalLogic: question.conditionalLogic || [],
        defaultNextQuestionId: question.defaultNextQuestionId || '',
        templateMapping: question.templateMapping || '',
        order: question.order,
      });
    } else {
      setFormData({
        text: '',
        type: 'text',
        category: 'personal',
        required: false,
        helpText: '',
        placeholder: '',
        options: [],
        validation: [],
        conditionalLogic: [],
        defaultNextQuestionId: '',
        templateMapping: '',
        order: 1,
      });
    }
    setErrors({});
    setActiveTab('basic');
  }, [question, isOpen]);

  const handleFieldChange = (field: string, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }));
    }
  };

  const handleAddOption = () => {
    const newOption: QuestionOption = {
      id: `opt_${Date.now()}`,
      label: '',
      value: '',
    };
    setFormData(prev => ({
      ...prev,
      options: [...prev.options, newOption],
    }));
  };

  const handleUpdateOption = (
    index: number,
    field: keyof QuestionOption,
    value: string
  ) => {
    setFormData(prev => ({
      ...prev,
      options: prev.options.map((opt, i) =>
        i === index ? { ...opt, [field]: value } : opt
      ),
    }));
  };

  const handleRemoveOption = (index: number) => {
    setFormData(prev => ({
      ...prev,
      options: prev.options.filter((_, i) => i !== index),
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    const validationErrors = validateQuestionData(formData);
    if (Object.keys(validationErrors).length > 0) {
      setErrors(validationErrors);
      return;
    }

    try {
      setSaving(true);
      setErrors({});

      if (isEditing && question) {
        const updateData: UpdateQuestionRequest = {
          id: question.id,
          ...formData,
        };
        await updateQuestion(interviewId, updateData);
      } else {
        const createData: CreateQuestionRequest = formData;
        await createQuestion(interviewId, createData);
      }

      onSave();
    } catch (err) {
      setErrors({ submit: 'Failed to save question. Please try again.' });
      console.error('Error saving question:', err);
    } finally {
      refetch();
      setSaving(false);
    }
  };

  const needsOptions = ['radio', 'select', 'checkbox'].includes(formData.type);

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className='max-w-4xl max-h-[90vh] overflow-y-auto'>
        <DialogHeader>
          <DialogTitle className='flex items-center space-x-2'>
            <HelpCircle className='h-5 w-5' />
            <span>{isEditing ? 'Edit Question' : 'Create New Question'}</span>
          </DialogTitle>
          <DialogDescription>
            {isEditing
              ? 'Update question details, options, and logic.'
              : 'Create a new interview question with options and conditional logic.'}
          </DialogDescription>
        </DialogHeader>

        <form onSubmit={handleSubmit}>
          {errors.submit && (
            <Alert variant='destructive' className='mb-4'>
              <AlertTriangle className='h-4 w-4' />
              <AlertDescription>{errors.submit}</AlertDescription>
            </Alert>
          )}

          <Tabs value={activeTab} onValueChange={setActiveTab}>
            <TabsList className='grid w-full grid-cols-3'>
              <TabsTrigger value='basic'>Basic Info</TabsTrigger>
              <TabsTrigger value='options'>Options</TabsTrigger>
              <TabsTrigger value='logic'>Logic & Mapping</TabsTrigger>
            </TabsList>

            <TabsContent value='basic' className='space-y-4'>
              <Card>
                <CardHeader>
                  <CardTitle className='text-lg'>Question Details</CardTitle>
                </CardHeader>
                <CardContent className='space-y-4'>
                  <div className='space-y-2'>
                    <Label htmlFor='text'>
                      Question Text <span className='text-red-500'>*</span>
                    </Label>
                    <Textarea
                      id='text'
                      value={formData.text}
                      onChange={e => handleFieldChange('text', e.target.value)}
                      placeholder='What is your full legal name?'
                      rows={2}
                      className={errors.text ? 'border-red-500' : ''}
                    />
                    {errors.text && (
                      <p className='text-sm text-red-500'>{errors.text}</p>
                    )}
                  </div>

                  <div className='grid grid-cols-2 gap-4'>
                    <div className='space-y-2'>
                      <Label htmlFor='type'>Question Type</Label>
                      <Select
                        value={formData.type}
                        onValueChange={(value: QuestionType) =>
                          handleFieldChange('type', value)
                        }
                      >
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          {QUESTION_TYPES.map(type => (
                            <SelectItem key={type.value} value={type.value}>
                              <div>
                                <div className='font-medium'>{type.label}</div>
                                <div className='text-xs text-[var(--custom-gray-medium)]'>
                                  {type.description}
                                </div>
                              </div>
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>

                    <div className='space-y-2'>
                      <Label htmlFor='category'>Category</Label>
                      <Select
                        value={formData.category}
                        onValueChange={(value: QuestionCategory) =>
                          handleFieldChange('category', value)
                        }
                      >
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          {QUESTION_CATEGORIES.map(category => (
                            <SelectItem
                              key={category.value}
                              value={category.value}
                            >
                              {category.label}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>
                  </div>

                  <div className='space-y-2'>
                    <Label htmlFor='helpText'>Help Text</Label>
                    <Input
                      id='helpText'
                      value={formData.helpText}
                      onChange={e =>
                        handleFieldChange('helpText', e.target.value)
                      }
                      placeholder='Additional guidance for the user'
                    />
                  </div>

                  <div className='space-y-2'>
                    <Label htmlFor='placeholder'>Placeholder Text</Label>
                    <Input
                      id='placeholder'
                      value={formData.placeholder}
                      onChange={e =>
                        handleFieldChange('placeholder', e.target.value)
                      }
                      placeholder='Placeholder text shown in input field'
                    />
                  </div>

                  <div className='flex items-center space-x-2'>
                    <Checkbox
                      id='required'
                      checked={formData.required}
                      onCheckedChange={checked =>
                        handleFieldChange('required', checked)
                      }
                    />
                    <Label htmlFor='required'>Required field</Label>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value='options' className='space-y-4'>
              {needsOptions ? (
                <Card>
                  <CardHeader>
                    <CardTitle className='text-lg flex items-center justify-between'>
                      Answer Options
                      <Button type='button' onClick={handleAddOption} size='sm'>
                        <Plus className='mr-2 h-4 w-4' />
                        Add Option
                      </Button>
                    </CardTitle>
                    <CardDescription>
                      Define the available choices for this question
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    {formData.options.length === 0 ? (
                      <div className='text-center py-8 text-[var(--custom-gray-medium)]'>
                        <List className='mx-auto h-8 w-8 mb-2' />
                        <p>No options added yet</p>
                        <Button
                          type='button'
                          onClick={handleAddOption}
                          variant='outline'
                          size='sm'
                          className='mt-2'
                        >
                          Add First Option
                        </Button>
                      </div>
                    ) : (
                      <div className='space-y-3'>
                        {formData.options.map((option, index) => (
                          <div
                            key={option.id}
                            className='flex items-center space-x-2'
                          >
                            <div className='flex-1 grid grid-cols-2 gap-2'>
                              <Input
                                placeholder='Option label (shown to user)'
                                value={option.label}
                                onChange={e =>
                                  handleUpdateOption(
                                    index,
                                    'label',
                                    e.target.value
                                  )
                                }
                              />
                              <Input
                                placeholder='Option value (stored in database)'
                                value={option.value}
                                onChange={e =>
                                  handleUpdateOption(
                                    index,
                                    'value',
                                    e.target.value
                                  )
                                }
                              />
                            </div>
                            <Button
                              type='button'
                              variant='outline'
                              size='sm'
                              onClick={() => handleRemoveOption(index)}
                            >
                              <Trash2 className='h-4 w-4' />
                            </Button>
                          </div>
                        ))}
                      </div>
                    )}
                  </CardContent>
                </Card>
              ) : (
                <Card>
                  <CardContent className='text-center py-8 text-[var(--custom-gray-medium)]'>
                    <Settings className='mx-auto h-8 w-8 mb-2' />
                    <p>This question type doesn't require options</p>
                  </CardContent>
                </Card>
              )}
            </TabsContent>

            <TabsContent value='logic' className='space-y-4'>
              <Card>
                <CardHeader>
                  <CardTitle className='text-lg'>Template Mapping</CardTitle>
                  <CardDescription>
                    Map this question to a document template variable
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className='space-y-2'>
                    <Label htmlFor='templateMapping'>Template Variable</Label>
                    <Input
                      id='templateMapping'
                      value={formData.templateMapping}
                      onChange={e =>
                        handleFieldChange('templateMapping', e.target.value)
                      }
                      placeholder='e.g., client_full_name'
                    />
                    <p className='text-sm text-[var(--custom-gray-medium)]'>
                      The template variable name where this answer will be
                      inserted
                    </p>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle className='text-lg'>Conditional Logic</CardTitle>
                  <CardDescription>
                    Define what happens after this question based on the answer
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className='text-center py-8 text-[var(--custom-gray-medium)]'>
                    <ArrowRight className='mx-auto h-8 w-8 mb-2' />
                    <p>Conditional logic builder coming soon</p>
                    <p className='text-sm'>
                      For now, questions will flow in order
                    </p>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>

          <DialogFooter className='mt-6'>
            <Button
              type='button'
              variant='outline'
              onClick={onClose}
              disabled={saving}
            >
              Cancel
            </Button>
            <Button type='submit' disabled={saving}>
              <Save className='mr-2 h-4 w-4' />
              {saving
                ? 'Saving...'
                : isEditing
                  ? 'Update Question'
                  : 'Create Question'}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
}
