'use client';

import React from 'react';
import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Checkbox } from '@/components/ui/checkbox';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { AlertCircle, Loader2 } from 'lucide-react';
import { useUserForm } from '@/hooks/use-user-form';
import { type UserFormProps } from '@/lib/validations/user';

export function UserForm({
  user,
  mode,
  onSave,
  isLoading = false,
}: UserFormProps) {
  const {
    form,
    isSubmitting,
    submitError,
    welonTrustUsers,
    loadingWelonTrust,
    watchedRole,
    watchedAssignedWelonTrust,
    availableSubroles,
    availablePermissions,
    onSubmit,
    handleCancel,
    handleRoleChange,
    handleSubroleChange,
    handlePermissionChange,
    handleWelonTrustChange,
  } = useUserForm({
    user,
    mode,
    onSave,
    isLoading,
  });

  return (
    <div className='space-y-6'>
      {/* Header */}
      <div className='flex items-center space-x-4'>
        <Button
          variant='ghost'
          size='sm'
          onClick={handleCancel}
          icon='arrow-left'
          iconSize='sm'
        >
          Back to Users
        </Button>
      </div>

      <div>
        <h1 className='text-3xl font-geologica font-semibold'>
          {mode === 'create' ? 'Create New User' : 'Edit User'}
        </h1>
        <p className='text-muted-foreground mt-2'>
          {mode === 'create'
            ? 'Add a new user to the system'
            : 'Update user details and permissions'}
        </p>
      </div>

      {/* Error Alert */}
      {submitError && (
        <Alert variant='destructive'>
          <AlertCircle className='h-4 w-4' />
          <AlertTitle>Error</AlertTitle>
          <AlertDescription>{submitError}</AlertDescription>
        </Alert>
      )}

      {/* Form */}
      <Form {...form}>
        <form onSubmit={onSubmit} className='space-y-6'>
          <Card>
            <CardHeader>
              <CardTitle>User Information</CardTitle>
              <CardDescription>
                Enter the user's basic information and role assignments
              </CardDescription>
            </CardHeader>
            <CardContent className='space-y-6'>
              <div className='grid grid-cols-2 gap-4'>
                {/* Name Field */}
                <FormField
                  control={form.control as any}
                  name='name'
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Name</FormLabel>
                      <FormControl>
                        <Input placeholder='Enter full name' {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                {/* Email Field */}
                <FormField
                  control={form.control as any}
                  name='email'
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Email</FormLabel>
                      <FormControl>
                        <Input
                          type='email'
                          placeholder='Enter email address'
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <div className='grid grid-cols-2 gap-4'>
                {/* Role Field */}
                <FormField
                  control={form.control as any}
                  name='role'
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Role</FormLabel>
                      <Select
                        onValueChange={value => handleRoleChange(value as any)}
                        defaultValue={field.value}
                      >
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder='Select role' />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          <SelectItem value='Member'>Member</SelectItem>
                          <SelectItem value='Administrator'>
                            Administrator
                          </SelectItem>
                          <SelectItem value='WelonTrust'>
                            Welon Trust
                          </SelectItem>
                          <SelectItem value='Professional'>
                            Professional
                          </SelectItem>
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                {/* Subrole Field */}
                <FormField
                  control={form.control as any}
                  name='subrole'
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Subrole</FormLabel>
                      <Select
                        onValueChange={handleSubroleChange}
                        defaultValue={field.value}
                      >
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder='Select subrole' />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          {availableSubroles.map(subrole => (
                            <SelectItem key={subrole} value={subrole}>
                              {subrole}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
            </CardContent>
          </Card>

          {/* Permissions */}
          <Card>
            <CardHeader>
              <CardTitle>Permissions</CardTitle>
              <CardDescription>
                Select the permissions for this user based on their role
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className='space-y-2'>
                {availablePermissions.map(permission => (
                  <div key={permission} className='flex items-center space-x-2'>
                    <Checkbox
                      id={`permission-${permission}`}
                      checked={form.watch('permissions').includes(permission)}
                      onCheckedChange={checked =>
                        handlePermissionChange(permission, checked as boolean)
                      }
                    />
                    <label
                      htmlFor={`permission-${permission}`}
                      className='text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70'
                    >
                      {permission}
                    </label>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* Welon Trust Assignment - Only show for Members */}
          {watchedRole === 'Member' && (
            <Card>
              <CardHeader>
                <CardTitle>Welon Trust Assignment</CardTitle>
                <CardDescription>
                  Assign a Welon Trust to this member for estate management
                </CardDescription>
              </CardHeader>
              <CardContent className='space-y-4'>
                {/* Welon Trust Selection Field */}
                <FormField
                  control={form.control as any}
                  name='assignedWelonTrust'
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Select Welon Trust</FormLabel>
                      <Select
                        onValueChange={handleWelonTrustChange}
                        defaultValue={
                          loadingWelonTrust ? 'loading' : field.value || 'none'
                        }
                        disabled={loadingWelonTrust}
                      >
                        <FormControl>
                          <SelectTrigger
                            className={
                              loadingWelonTrust
                                ? 'cursor-not-allowed opacity-60'
                                : ''
                            }
                          >
                            {loadingWelonTrust ? (
                              <div className='flex items-center gap-2'>
                                <Loader2 className='h-4 w-4 animate-spin' />
                                <span>Loading Welon Trust users...</span>
                              </div>
                            ) : (
                              <SelectValue placeholder='Select existing Welon Trust or invite new' />
                            )}
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          <SelectItem value='none'>No assignment</SelectItem>
                          {loadingWelonTrust ? (
                            <SelectItem value='loading' disabled>
                              <div className='flex items-center gap-2'>
                                <Loader2 className='h-4 w-4 animate-spin' />
                                <span>Loading Welon Trust users...</span>
                              </div>
                            </SelectItem>
                          ) : (
                            <>
                              {welonTrustUsers.map(wt => (
                                <SelectItem key={wt.id} value={wt.id}>
                                  {wt.name} ({wt.email})
                                </SelectItem>
                              ))}
                              <SelectItem value='invite_new'>
                                Invite new Welon Trust via email
                              </SelectItem>
                            </>
                          )}
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                {/* New Welon Trust Email Field - Show when "invite_new" is selected */}
                {watchedAssignedWelonTrust === 'invite_new' && (
                  <FormField
                    control={form.control as any}
                    name='newWelonTrustEmail'
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>New Welon Trust Email</FormLabel>
                        <FormControl>
                          <Input
                            type='email'
                            placeholder='Enter email to invite new Welon Trust'
                            {...field}
                          />
                        </FormControl>
                        <p className='text-sm text-muted-foreground'>
                          An invitation will be sent to this email to join as
                          Welon Trust for this member.
                        </p>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                )}

                {/* Show current assignment info for edit mode */}
                {mode === 'edit' && user?.assignedWelonTrust && (
                  <div className='bg-blue-50 p-3 rounded-md'>
                    <p className='text-sm font-medium text-blue-800'>
                      Current Assignment:
                    </p>
                    <p className='text-sm text-blue-700'>
                      {user.assignedWelonTrust.welonTrustName} (
                      {user.assignedWelonTrust.welonTrustEmail})
                    </p>
                    <p className='text-xs text-blue-600'>
                      Status: {user.assignedWelonTrust.status} • Assigned:{' '}
                      {new Date(
                        user.assignedWelonTrust.assignedAt
                      ).toLocaleDateString()}
                    </p>
                  </div>
                )}
              </CardContent>
            </Card>
          )}

          {/* Actions */}
          <div className='flex justify-end space-x-4'>
            <Button variant='outline' onClick={handleCancel} icon='close'>
              Cancel
            </Button>
            <Button
              type='submit'
              icon={mode === 'create' ? 'plus-circle' : 'save'}
              disabled={isSubmitting}
            >
              {isSubmitting
                ? mode === 'create'
                  ? 'Creating...'
                  : 'Saving...'
                : mode === 'create'
                  ? 'Create User'
                  : 'Save Changes'}
            </Button>
          </div>
        </form>
      </Form>
    </div>
  );
}
