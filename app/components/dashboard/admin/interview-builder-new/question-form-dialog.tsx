'use client';

import React, { useState, useEffect } from 'react';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Checkbox } from '@/components/ui/checkbox';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Badge } from '@/components/ui/badge';
import {
  Save,
  AlertTriangle,
  Plus,
  Trash2,
  <PERSON><PERSON><PERSON><PERSON>,
  ArrowRight,
} from 'lucide-react';
import {
  Question,
  QuestionOption,
  QuestionType,
  CreateQuestionNewRequest,
  UpdateQuestionNewRequest,
  QuestionFormData,
  ValidationErrors,
} from '@/types/interview-builder-new';
import {
  createQuestion,
  updateQuestion,
  validateQuestionData,
} from '@/lib/api/interview-builder-new';

interface QuestionFormDialogProps {
  interviewId: string;
  question: Question | null;
  existingQuestions: Question[];
  isOpen: boolean;
  onClose: () => void;
  onSave: () => void;
}

const QUESTION_TYPES: {
  value: QuestionType;
  label: string;
  description: string;
}[] = [
  { value: 'text', label: 'Text Input', description: 'Single line text input' },
  {
    value: 'radio',
    label: 'Radio Buttons',
    description: 'Single choice from options',
  },
  {
    value: 'select',
    label: 'Dropdown',
    description: 'Single choice from dropdown',
  },
  {
    value: 'checkbox',
    label: 'Checkboxes',
    description: 'Multiple choice options',
  },
];

export function QuestionFormDialog({
  interviewId,
  question,
  existingQuestions,
  isOpen,
  onClose,
  onSave,
}: QuestionFormDialogProps) {
  const [formData, setFormData] = useState<QuestionFormData>({
    text: '',
    type: 'text',
    options: [],
    isRequired: true,
    conditionalLogic: [],
  });

  const [errors, setErrors] = useState<ValidationErrors>({});
  const [saving, setSaving] = useState(false);
  const [activeTab, setActiveTab] = useState('basic');

  const isEditing = !!question;

  useEffect(() => {
    if (question) {
      setFormData({
        text: question.text,
        type: question.type,
        options: question.options || [],
        isRequired: question.isRequired || true,
        conditionalLogic: question.conditionalLogic || [],
      });
    } else {
      setFormData({
        text: '',
        type: 'text',
        options: [],
        isRequired: true,
        conditionalLogic: [],
      });
    }
    setErrors({});
    setActiveTab('basic');
  }, [question, isOpen]);

  const handleFieldChange = (field: keyof QuestionFormData, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }));
    }
  };

  const handleAddOption = () => {
    const newOption: QuestionOption = {
      id: `opt_${Date.now()}`,
      label: '',
      value: '',
    };
    setFormData(prev => ({
      ...prev,
      options: [...prev.options, newOption],
    }));
  };

  const handleUpdateOption = (
    index: number,
    field: keyof QuestionOption,
    value: string
  ) => {
    setFormData(prev => ({
      ...prev,
      options: prev.options.map((opt, i) =>
        i === index ? { ...opt, [field]: value } : opt
      ),
    }));
  };

  const handleRemoveOption = (index: number) => {
    setFormData(prev => ({
      ...prev,
      options: prev.options.filter((_, i) => i !== index),
    }));
  };

  const handleSetBranching = (optionIndex: number, nextQuestionId: string) => {
    setFormData(prev => ({
      ...prev,
      options: prev.options.map((opt, i) =>
        i === optionIndex
          ? { ...opt, nextQuestionId: nextQuestionId || undefined }
          : opt
      ),
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    const validationErrors = validateQuestionData(formData);
    if (Object.keys(validationErrors).length > 0) {
      setErrors(validationErrors);
      return;
    }

    try {
      setSaving(true);
      setErrors({});

      if (isEditing && question) {
        const updateData: UpdateQuestionNewRequest = {
          questionId: question.questionId,
          text: formData.text,
          type: formData.type,
          options: formData.options,
          isRequired: formData.isRequired,
          conditionalLogic: formData.conditionalLogic,
        };
        await updateQuestion(interviewId, updateData);
      } else {
        const createData: CreateQuestionNewRequest = {
          text: formData.text,
          type: formData.type,
          options: formData.options,
          isRequired: formData.isRequired,
          conditionalLogic: formData.conditionalLogic,
        };
        await createQuestion(interviewId, createData);
      }

      onSave();
    } catch (err) {
      setErrors({ submit: 'Failed to save question. Please try again.' });
      console.error('Error saving question:', err);
    } finally {
      setSaving(false);
    }
  };

  const needsOptions = ['radio', 'select', 'checkbox'].includes(formData.type);
  const supportsBranching = ['radio', 'select'].includes(formData.type);

  // Get available questions for branching (excluding current question)
  const availableQuestions = existingQuestions.filter(
    q => !question || q.questionId !== question.questionId
  );

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className='max-w-4xl max-h-[90vh] overflow-y-auto'>
        <DialogHeader>
          <DialogTitle className='flex items-center space-x-2'>
            <HelpCircle className='h-5 w-5' />
            <span>{isEditing ? 'Edit Question' : 'Create New Question'}</span>
          </DialogTitle>
          <DialogDescription>
            {isEditing
              ? 'Update question details, options, and logic.'
              : 'Create a new interview question with options and conditional logic.'}
          </DialogDescription>
        </DialogHeader>

        <form onSubmit={handleSubmit}>
          {errors.submit && (
            <Alert variant='destructive' className='mb-4'>
              <AlertTriangle className='h-4 w-4' />
              <AlertDescription>{errors.submit}</AlertDescription>
            </Alert>
          )}

          <Tabs value={activeTab} onValueChange={setActiveTab}>
            <TabsList className='grid w-full grid-cols-3'>
              <TabsTrigger value='basic'>Basic Info</TabsTrigger>
              <TabsTrigger value='options'>Options</TabsTrigger>
              <TabsTrigger value='branching'>Branching</TabsTrigger>
            </TabsList>

            <TabsContent value='basic' className='space-y-4'>
              <Card>
                <CardHeader>
                  <CardTitle className='text-lg'>Question Details</CardTitle>
                </CardHeader>
                <CardContent className='space-y-4'>
                  <div className='space-y-2'>
                    <Label htmlFor='text'>
                      Question Text <span className='text-red-500'>*</span>
                    </Label>
                    <Textarea
                      id='text'
                      value={formData.text}
                      onChange={e => handleFieldChange('text', e.target.value)}
                      placeholder='What is your full legal name?'
                      rows={2}
                    />
                    {errors.text && (
                      <p className='text-sm text-red-600'>{errors.text}</p>
                    )}
                  </div>

                  <div className='space-y-2'>
                    <Label htmlFor='type'>
                      Question Type <span className='text-red-500'>*</span>
                    </Label>
                    <Select
                      value={formData.type}
                      onValueChange={(value: QuestionType) =>
                        handleFieldChange('type', value)
                      }
                    >
                      <SelectTrigger>
                        <SelectValue placeholder='Select question type' />
                      </SelectTrigger>
                      <SelectContent>
                        {QUESTION_TYPES.map(type => (
                          <SelectItem key={type.value} value={type.value}>
                            <div>
                              <div className='font-medium'>{type.label}</div>
                              <div className='text-sm text-gray-500'>
                                {type.description}
                              </div>
                            </div>
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    {errors.type && (
                      <p className='text-sm text-red-600'>{errors.type}</p>
                    )}
                  </div>

                  {/*<div className='flex items-center space-x-2'>*/}
                  {/*  <Checkbox*/}
                  {/*    id='required'*/}
                  {/*    checked={formData.isRequired}*/}
                  {/*    onCheckedChange={checked =>*/}
                  {/*      handleFieldChange('isRequired', checked)*/}
                  {/*    }*/}
                  {/*  />*/}
                  {/*  <Label htmlFor='required'>Required field</Label>*/}
                  {/*</div>*/}
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value='options' className='space-y-4'>
              <Card>
                <CardHeader>
                  <CardTitle className='text-lg'>Answer Options</CardTitle>
                  <CardDescription>
                    {needsOptions
                      ? 'Define the available answer choices for this question.'
                      : 'This question type does not require predefined options.'}
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  {needsOptions ? (
                    <div className='space-y-4'>
                      {formData.options.map((option, index) => (
                        <div
                          key={option.id}
                          className='flex items-center space-x-2'
                        >
                          <div className='flex-1 grid grid-cols-2 gap-2'>
                            <Input
                              placeholder='Option label'
                              value={option.label}
                              onChange={e =>
                                handleUpdateOption(
                                  index,
                                  'label',
                                  e.target.value
                                )
                              }
                            />
                            <Input
                              placeholder='Option value'
                              value={option.value}
                              onChange={e =>
                                handleUpdateOption(
                                  index,
                                  'value',
                                  e.target.value
                                )
                              }
                            />
                          </div>
                          <Button
                            type='button'
                            variant='ghost'
                            size='sm'
                            onClick={() => handleRemoveOption(index)}
                          >
                            <Trash2 className='h-4 w-4' />
                          </Button>
                        </div>
                      ))}

                      <Button
                        type='button'
                        variant='outline'
                        onClick={handleAddOption}
                        className='w-full'
                      >
                        <Plus className='mr-2 h-4 w-4' />
                        Add Option
                      </Button>

                      {errors.options && (
                        <p className='text-sm text-red-600'>{errors.options}</p>
                      )}
                    </div>
                  ) : (
                    <div className='text-center py-8 text-gray-500'>
                      <HelpCircle className='mx-auto h-8 w-8 mb-2' />
                      <p>
                        Text input questions don't require predefined options.
                      </p>
                    </div>
                  )}
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value='branching' className='space-y-4'>
              <Card>
                <CardHeader>
                  <CardTitle className='text-lg'>
                    Conditional Branching
                  </CardTitle>
                  <CardDescription>
                    {supportsBranching
                      ? 'Set up conditional logic to show different questions based on answers.'
                      : 'Branching logic is only available for radio and dropdown questions.'}
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  {supportsBranching && formData.options.length > 0 ? (
                    <div className='space-y-4'>
                      {formData.options.map((option, index) => (
                        <div key={option.id} className='border rounded-lg p-4'>
                          <div className='flex items-center justify-between mb-2'>
                            <div className='font-medium'>
                              {option.label || `Option ${index + 1}`}
                            </div>
                            {option.nextQuestionId && (
                              <Badge variant='secondary'>
                                <ArrowRight className='mr-1 h-3 w-3' />
                                Has Branch
                              </Badge>
                            )}
                          </div>
                          <div className='space-y-2'>
                            <Label>Next Question</Label>
                            <Select
                              value={option.nextQuestionId || '__none__'}
                              onValueChange={value =>
                                handleSetBranching(
                                  index,
                                  value === '__none__' ? '' : value
                                )
                              }
                            >
                              <SelectTrigger>
                                <SelectValue placeholder='Continue to next question' />
                              </SelectTrigger>
                              <SelectContent>
                                <SelectItem value='__none__'>
                                  Continue to next question
                                </SelectItem>
                                {availableQuestions.map(q => (
                                  <SelectItem
                                    key={q.questionId}
                                    value={q.questionId}
                                  >
                                    {q.text}
                                  </SelectItem>
                                ))}
                              </SelectContent>
                            </Select>
                          </div>
                        </div>
                      ))}
                    </div>
                  ) : (
                    <div className='text-center py-8 text-gray-500'>
                      <ArrowRight className='mx-auto h-8 w-8 mb-2' />
                      <p>
                        {!supportsBranching
                          ? 'Branching is only available for radio and dropdown questions.'
                          : 'Add answer options first to set up branching logic.'}
                      </p>
                    </div>
                  )}
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>

          <DialogFooter className='mt-6'>
            <Button
              type='button'
              variant='outline'
              onClick={onClose}
              disabled={saving}
            >
              Cancel
            </Button>
            <Button type='submit' disabled={saving}>
              {saving ? (
                <>
                  <div className='animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2' />
                  Saving...
                </>
              ) : (
                <>
                  <Save className='mr-2 h-4 w-4' />
                  {isEditing ? 'Update Question' : 'Create Question'}
                </>
              )}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
}
