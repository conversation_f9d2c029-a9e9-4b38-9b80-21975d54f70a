'use client';

import React, { useState } from 'react';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Badge } from '@/components/ui/badge';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
  Plus,
  MessageSquare,
  AlertTriangle,
  Edit,
  Trash2,
  MoreHorizontal,
  ArrowRight,
  Settings,
} from 'lucide-react';
import {
  InterviewNew,
  InterviewVersionNew,
  Question,
} from '@/types/interview-builder-new';
import { deleteQuestion } from '@/lib/api/interview-builder-new';
import { QuestionFormDialog } from './question-form-dialog';

interface QuestionBuilderProps {
  interviewId: string;
  interview: InterviewNew;
  version: InterviewVersionNew | null;
  onQuestionsUpdated: () => void;
  refetch: () => void;
}

export function QuestionBuilder({
  interviewId,
  interview,
  version,
  onQuestionsUpdated,
  refetch,
}: QuestionBuilderProps) {
  const queryClient = useQueryClient();
  const [showCreateDialog, setShowCreateDialog] = useState(false);
  const [editingQuestion, setEditingQuestion] = useState<Question | null>(null);
  const [error, setError] = useState<string | null>(null);

  // Delete mutation
  const deleteMutation = useMutation({
    mutationFn: ({
      interviewId,
      questionId,
    }: {
      interviewId: string;
      questionId: string;
    }) => deleteQuestion(interviewId, questionId),
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: ['interview-with-version', interviewId],
      });
      onQuestionsUpdated();
    },
    onError: error => {
      setError('Failed to delete question. Please try again.');
      console.error('Delete question error:', error);
    },
  });

  const questions = version?.questions || [];
  const sortedQuestions = [...questions].sort((a, b) => a.order - b.order);

  // Helper functions for conditional logic
  const getConditionalQuestionIds = (): Set<string> => {
    const conditionalIds = new Set<string>();
    sortedQuestions.forEach(question => {
      question.options?.forEach(option => {
        if (option.nextQuestionId) {
          conditionalIds.add(option.nextQuestionId);
        }
      });
    });
    return conditionalIds;
  };

  const getParentQuestion = (
    conditionalQuestionId: string
  ): Question | null => {
    return (
      sortedQuestions.find(question =>
        question.options?.some(
          option => option.nextQuestionId === conditionalQuestionId
        )
      ) || null
    );
  };

  const getConditionalQuestionsForParent = (
    parentQuestion: Question
  ): Question[] => {
    const conditionalQuestionIds = new Set<string>();
    parentQuestion.options?.forEach(option => {
      if (option.nextQuestionId) {
        conditionalQuestionIds.add(option.nextQuestionId);
      }
    });

    return sortedQuestions
      .filter(question => conditionalQuestionIds.has(question.questionId))
      .sort((a, b) => a.order - b.order);
  };

  // Filter out conditional questions from main list
  const conditionalQuestionIds = getConditionalQuestionIds();
  const mainQuestions = sortedQuestions.filter(
    question => !conditionalQuestionIds.has(question.questionId)
  );

  const handleCreateQuestion = () => {
    setEditingQuestion(null);
    setShowCreateDialog(true);
  };

  const handleEditQuestion = (question: Question) => {
    setEditingQuestion(question);
    setShowCreateDialog(true);
  };

  const handleDeleteQuestion = async (question: Question) => {
    if (
      window.confirm(
        `Are you sure you want to delete "${question.text}"? This action cannot be undone.`
      )
    ) {
      deleteMutation.mutate({ interviewId, questionId: question.questionId });
    }
  };

  const handleDialogSave = () => {
    setShowCreateDialog(false);
    setEditingQuestion(null);
    refetch();
  };

  const getQuestionTypeLabel = (type: string) => {
    const typeLabels: Record<string, string> = {
      text: 'Text Input',
      radio: 'Radio Buttons',
      select: 'Dropdown',
      checkbox: 'Checkboxes',
    };
    return typeLabels[type] || type;
  };

  const getQuestionTypeBadgeVariant = (type: string) => {
    const variants: Record<string, 'default' | 'secondary' | 'outline'> = {
      text: 'default',
      radio: 'secondary',
      select: 'outline',
      checkbox: 'secondary',
    };
    return variants[type] || 'default';
  };

  const hasConditionalLogic = (question: Question) => {
    return question.options?.some(opt => opt.nextQuestionId) || false;
  };

  return (
    <div className='space-y-6'>
      {/* Header */}
      <div className='flex justify-between items-center'>
        <div>
          <h2 className='text-2xl font-bold text-[var(--custom-gray-dark)]'>
            Questions
          </h2>
          <p className='text-[var(--custom-gray-medium)]'>
            Manage interview questions and their flow logic
          </p>
        </div>
        <Button onClick={handleCreateQuestion}>
          <Plus className='mr-2 h-4 w-4' />
          Add Question
        </Button>
      </div>

      {/* Error Alert */}
      {error && (
        <Alert variant='destructive'>
          <AlertTriangle className='h-4 w-4' />
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      {/* Questions List */}
      {sortedQuestions.length === 0 ? (
        <Card>
          <CardContent className='text-center py-12'>
            <MessageSquare className='mx-auto h-12 w-12 text-[var(--custom-gray-medium)]' />
            <h3 className='mt-2 text-sm font-medium text-[var(--custom-gray-dark)]'>
              No questions yet
            </h3>
            <p className='mt-1 text-sm text-[var(--custom-gray-medium)]'>
              Get started by adding your first interview question.
            </p>
            <div className='mt-6'>
              <Button onClick={handleCreateQuestion}>
                <Plus className='mr-2 h-4 w-4' />
                Add First Question
              </Button>
            </div>
          </CardContent>
        </Card>
      ) : (
        <Card>
          <CardHeader>
            <CardTitle>Question Flow</CardTitle>
            <CardDescription>
              Questions are presented in the order shown below
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Order</TableHead>
                  <TableHead>Question</TableHead>
                  <TableHead>Type</TableHead>
                  <TableHead>Options</TableHead>
                  <TableHead>Logic</TableHead>
                  <TableHead>Required</TableHead>
                  <TableHead className='text-right'>Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {mainQuestions.map((question, index) => {
                  const conditionalQuestions =
                    getConditionalQuestionsForParent(question);

                  return (
                    <React.Fragment key={question.questionId}>
                      {/* Main Question Row */}
                      <TableRow>
                        <TableCell>
                          <Badge variant='outline'>{question.order}</Badge>
                        </TableCell>
                        <TableCell>
                          <div className='max-w-md'>
                            <div className='font-medium text-[var(--custom-gray-dark)] truncate'>
                              {question.text}
                            </div>
                          </div>
                        </TableCell>
                        <TableCell>
                          <Badge
                            variant={getQuestionTypeBadgeVariant(question.type)}
                          >
                            {getQuestionTypeLabel(question.type)}
                          </Badge>
                        </TableCell>
                        <TableCell>
                          <div className='text-sm text-[var(--custom-gray-medium)]'>
                            {question.options?.length || 0} options
                          </div>
                        </TableCell>
                        <TableCell>
                          {hasConditionalLogic(question) ? (
                            <Badge variant='secondary' className='text-xs'>
                              <ArrowRight className='mr-1 h-3 w-3' />
                              Branching
                            </Badge>
                          ) : (
                            <span className='text-sm text-[var(--custom-gray-medium)]'>
                              Linear
                            </span>
                          )}
                        </TableCell>
                        <TableCell>
                          {question.isRequired ? (
                            <Badge variant='destructive' className='text-xs'>
                              Required
                            </Badge>
                          ) : (
                            <span className='text-sm text-[var(--custom-gray-medium)]'>
                              Optional
                            </span>
                          )}
                        </TableCell>
                        <TableCell className='text-right'>
                          <DropdownMenu>
                            <DropdownMenuTrigger asChild>
                              <Button variant='ghost' size='sm'>
                                <MoreHorizontal className='h-4 w-4' />
                              </Button>
                            </DropdownMenuTrigger>
                            <DropdownMenuContent align='end'>
                              <DropdownMenuItem
                                onClick={() => handleEditQuestion(question)}
                              >
                                <Edit className='mr-2 h-4 w-4' />
                                Edit Question
                              </DropdownMenuItem>
                              <DropdownMenuItem
                                onClick={() => handleDeleteQuestion(question)}
                                className='text-red-600'
                              >
                                <Trash2 className='mr-2 h-4 w-4' />
                                Delete
                              </DropdownMenuItem>
                            </DropdownMenuContent>
                          </DropdownMenu>
                        </TableCell>
                      </TableRow>

                      {/* Conditional Questions Rows */}
                      {conditionalQuestions.map(conditionalQuestion => (
                        <TableRow
                          key={conditionalQuestion.questionId}
                          className='bg-gray-50/50 border-l-4 border-l-blue-200'
                        >
                          <TableCell>
                            <div className='flex items-center space-x-2'>
                              <div className='w-4 h-4 flex items-center justify-center'>
                                <ArrowRight className='h-3 w-3 text-blue-500' />
                              </div>
                              <Badge variant='outline' className='text-xs'>
                                {conditionalQuestion.order}
                              </Badge>
                            </div>
                          </TableCell>
                          <TableCell>
                            <div className='max-w-md pl-6'>
                              <div className='font-medium text-[var(--custom-gray-dark)] truncate text-sm'>
                                {conditionalQuestion.text}
                              </div>
                              <div className='text-xs text-blue-600 mt-1'>
                                Conditional Question
                              </div>
                            </div>
                          </TableCell>
                          <TableCell>
                            <Badge
                              variant={getQuestionTypeBadgeVariant(
                                conditionalQuestion.type
                              )}
                              className='text-xs'
                            >
                              {getQuestionTypeLabel(conditionalQuestion.type)}
                            </Badge>
                          </TableCell>
                          <TableCell>
                            <div className='text-sm text-[var(--custom-gray-medium)]'>
                              {conditionalQuestion.options?.length || 0} options
                            </div>
                          </TableCell>
                          <TableCell>
                            {hasConditionalLogic(conditionalQuestion) ? (
                              <Badge variant='secondary' className='text-xs'>
                                <ArrowRight className='mr-1 h-3 w-3' />
                                Branching
                              </Badge>
                            ) : (
                              <span className='text-sm text-[var(--custom-gray-medium)]'>
                                Linear
                              </span>
                            )}
                          </TableCell>
                          <TableCell>
                            {conditionalQuestion.isRequired ? (
                              <Badge variant='destructive' className='text-xs'>
                                Required
                              </Badge>
                            ) : (
                              <span className='text-sm text-[var(--custom-gray-medium)]'>
                                Optional
                              </span>
                            )}
                          </TableCell>
                          <TableCell className='text-right'>
                            <DropdownMenu>
                              <DropdownMenuTrigger asChild>
                                <Button variant='ghost' size='sm'>
                                  <MoreHorizontal className='h-4 w-4' />
                                </Button>
                              </DropdownMenuTrigger>
                              <DropdownMenuContent align='end'>
                                <DropdownMenuItem
                                  onClick={() =>
                                    handleEditQuestion(conditionalQuestion)
                                  }
                                >
                                  <Edit className='mr-2 h-4 w-4' />
                                  Edit Question
                                </DropdownMenuItem>
                                <DropdownMenuItem
                                  onClick={() =>
                                    handleDeleteQuestion(conditionalQuestion)
                                  }
                                  className='text-red-600'
                                >
                                  <Trash2 className='mr-2 h-4 w-4' />
                                  Delete
                                </DropdownMenuItem>
                              </DropdownMenuContent>
                            </DropdownMenu>
                          </TableCell>
                        </TableRow>
                      ))}
                    </React.Fragment>
                  );
                })}
              </TableBody>
            </Table>
          </CardContent>
        </Card>
      )}

      {/* Question Form Dialog */}
      <QuestionFormDialog
        interviewId={interviewId}
        question={editingQuestion}
        existingQuestions={sortedQuestions}
        isOpen={showCreateDialog}
        onClose={() => {
          setShowCreateDialog(false);
          setEditingQuestion(null);
        }}
        onSave={handleDialogSave}
      />
    </div>
  );
}
