'use client';

import React, { useState, useEffect } from 'react';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Checkbox } from '@/components/ui/checkbox';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import {
  Play,
  RotateCcw,
  ArrowRight,
  ArrowLeft,
  CheckCircle,
  AlertTriangle,
  Eye,
} from 'lucide-react';
import {
  InterviewNew,
  InterviewVersionNew,
  Question,
  QuestionOption,
} from '@/types/interview-builder-new';

interface InterviewPreviewProps {
  interview: InterviewNew;
  version: InterviewVersionNew | null;
}

interface PreviewState {
  currentQuestionIndex: number;
  responses: Record<string, any>;
  questionHistory: number[];
  isComplete: boolean;
  isStarted: boolean;
}

export function InterviewPreview({
  interview,
  version,
}: InterviewPreviewProps) {
  const [previewState, setPreviewState] = useState<PreviewState>({
    currentQuestionIndex: 0,
    responses: {},
    questionHistory: [],
    isComplete: false,
    isStarted: false,
  });

  const questions = version?.questions || [];
  const sortedQuestions = [...questions].sort((a, b) => a.order - b.order);

  const currentQuestion = sortedQuestions[previewState.currentQuestionIndex];
  const currentResponse =
    previewState.responses[currentQuestion?.questionId] || '';

  const handleStart = () => {
    setPreviewState({
      currentQuestionIndex: 0,
      responses: {},
      questionHistory: [],
      isComplete: false,
      isStarted: true,
    });
  };

  const handleReset = () => {
    setPreviewState({
      currentQuestionIndex: 0,
      responses: {},
      questionHistory: [],
      isComplete: false,
      isStarted: false,
    });
  };

  const handleResponseChange = (value: any) => {
    if (!currentQuestion) return;

    setPreviewState(prev => ({
      ...prev,
      responses: {
        ...prev.responses,
        [currentQuestion.questionId]: value,
      },
    }));
  };

  const getNextQuestionIndex = (question: Question, response: any): number => {
    // Check for branching logic
    if (question.type === 'radio' || question.type === 'select') {
      const selectedOption = question.options?.find(
        opt => opt.value === response
      );
      if (selectedOption?.nextQuestionId) {
        const targetIndex = sortedQuestions.findIndex(
          q => q.questionId === selectedOption.nextQuestionId
        );
        if (targetIndex !== -1) {
          return targetIndex;
        }
      }
    }

    // Default: next question in order
    return previewState.currentQuestionIndex + 1;
  };

  const handleNext = () => {
    if (!currentQuestion) return;

    const response = previewState.responses[currentQuestion.questionId];

    // Validate required fields
    if (currentQuestion.isRequired && (!response || response === '')) {
      return; // Don't proceed if required field is empty
    }

    const nextIndex = getNextQuestionIndex(currentQuestion, response);

    setPreviewState(prev => ({
      ...prev,
      currentQuestionIndex: nextIndex,
      questionHistory: [...prev.questionHistory, prev.currentQuestionIndex],
      isComplete: nextIndex >= sortedQuestions.length,
    }));
  };

  const handlePrevious = () => {
    if (previewState.questionHistory.length === 0) return;

    const previousIndex =
      previewState.questionHistory[previewState.questionHistory.length - 1];

    setPreviewState(prev => ({
      ...prev,
      currentQuestionIndex: previousIndex,
      questionHistory: prev.questionHistory.slice(0, -1),
      isComplete: false,
    }));
  };

  const canProceed = () => {
    if (!currentQuestion) return false;
    const response = previewState.responses[currentQuestion.questionId];
    return !currentQuestion.isRequired || (response && response !== '');
  };

  const renderQuestionInput = (question: Question) => {
    const response = previewState.responses[question.questionId] || '';

    switch (question.type) {
      case 'text':
        return (
          <Input
            value={response}
            onChange={e => handleResponseChange(e.target.value)}
            placeholder='Enter your answer...'
            className='w-full'
          />
        );

      case 'radio':
        return (
          <RadioGroup
            value={response}
            onValueChange={handleResponseChange}
            className='space-y-2'
          >
            {question.options?.map(option => (
              <div key={option.id} className='flex items-center space-x-2'>
                <RadioGroupItem value={option.value} id={option.id} />
                <Label htmlFor={option.id} className='flex-1 cursor-pointer'>
                  {option.label}
                  {option.nextQuestionId && (
                    <Badge variant='outline' className='ml-2 text-xs'>
                      <ArrowRight className='mr-1 h-3 w-3' />
                      Branches
                    </Badge>
                  )}
                </Label>
              </div>
            ))}
          </RadioGroup>
        );

      case 'select':
        return (
          <Select value={response} onValueChange={handleResponseChange}>
            <SelectTrigger>
              <SelectValue placeholder='Select an option...' />
            </SelectTrigger>
            <SelectContent>
              {question.options?.map(option => (
                <SelectItem key={option.id} value={option.value}>
                  <div className='flex items-center justify-between w-full'>
                    <span>{option.label}</span>
                    {option.nextQuestionId && (
                      <Badge variant='outline' className='ml-2 text-xs'>
                        <ArrowRight className='mr-1 h-3 w-3' />
                        Branches
                      </Badge>
                    )}
                  </div>
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        );

      case 'checkbox':
        const checkboxResponses = Array.isArray(response) ? response : [];
        return (
          <div className='space-y-2'>
            {question.options?.map(option => (
              <div key={option.id} className='flex items-center space-x-2'>
                <Checkbox
                  id={option.id}
                  checked={checkboxResponses.includes(option.value)}
                  onCheckedChange={checked => {
                    const newResponses = checked
                      ? [...checkboxResponses, option.value]
                      : checkboxResponses.filter(v => v !== option.value);
                    handleResponseChange(newResponses);
                  }}
                />
                <Label htmlFor={option.id} className='flex-1 cursor-pointer'>
                  {option.label}
                </Label>
              </div>
            ))}
          </div>
        );

      default:
        return <div>Unsupported question type: {question.type}</div>;
    }
  };

  if (sortedQuestions.length === 0) {
    return (
      <Card>
        <CardContent className='text-center py-12'>
          <Eye className='mx-auto h-12 w-12 text-[var(--custom-gray-medium)]' />
          <h3 className='mt-2 text-sm font-medium text-[var(--custom-gray-dark)]'>
            No questions to preview
          </h3>
          <p className='mt-1 text-sm text-[var(--custom-gray-medium)]'>
            Add some questions to see the interview preview.
          </p>
        </CardContent>
      </Card>
    );
  }

  if (!previewState.isStarted) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className='flex items-center'>
            <Eye className='mr-2 h-5 w-5' />
            Interview Preview
          </CardTitle>
          <CardDescription>
            Test your interview flow and see how it will appear to users
          </CardDescription>
        </CardHeader>
        <CardContent className='space-y-4'>
          <div className='bg-blue-50 border border-blue-200 rounded-lg p-4'>
            <h3 className='font-medium text-blue-900 mb-2'>{interview.name}</h3>
            {interview.description && (
              <p className='text-blue-700 text-sm mb-3'>
                {interview.description}
              </p>
            )}
            <div className='flex items-center justify-between text-sm text-blue-600'>
              <span>{sortedQuestions.length} questions</span>
              {version && (
                <Badge variant='outline'>Version {version.versionNumber}</Badge>
              )}
            </div>
          </div>

          <Button onClick={handleStart} className='w-full'>
            <Play className='mr-2 h-4 w-4' />
            Start Preview
          </Button>
        </CardContent>
      </Card>
    );
  }

  if (previewState.isComplete) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className='flex items-center text-green-600'>
            <CheckCircle className='mr-2 h-5 w-5' />
            Interview Complete
          </CardTitle>
          <CardDescription>
            You've reached the end of the interview
          </CardDescription>
        </CardHeader>
        <CardContent className='space-y-4'>
          <div className='bg-green-50 border border-green-200 rounded-lg p-4'>
            <h3 className='font-medium text-green-900 mb-2'>
              Responses Summary
            </h3>
            <div className='space-y-2 text-sm'>
              {Object.entries(previewState.responses).map(
                ([questionId, response]) => {
                  const question = sortedQuestions.find(
                    q => q.questionId === questionId
                  );
                  if (!question) return null;

                  return (
                    <div key={questionId} className='flex justify-between'>
                      <span className='text-green-700 truncate max-w-xs'>
                        {question.text}
                      </span>
                      <span className='text-green-600 font-medium'>
                        {Array.isArray(response)
                          ? response.join(', ')
                          : response}
                      </span>
                    </div>
                  );
                }
              )}
            </div>
          </div>

          <div className='flex space-x-2'>
            <Button
              onClick={handlePrevious}
              variant='outline'
              className='flex-1'
            >
              <ArrowLeft className='mr-2 h-4 w-4' />
              Go Back
            </Button>
            <Button onClick={handleReset} className='flex-1'>
              <RotateCcw className='mr-2 h-4 w-4' />
              Start Over
            </Button>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <div className='flex items-center justify-between'>
          <CardTitle className='flex items-center'>
            <Eye className='mr-2 h-5 w-5' />
            Question {previewState.currentQuestionIndex + 1} of{' '}
            {sortedQuestions.length}
          </CardTitle>
          <Button onClick={handleReset} variant='outline' size='sm'>
            <RotateCcw className='mr-2 h-4 w-4' />
            Reset
          </Button>
        </div>
        <CardDescription>
          Preview how this question will appear to users
        </CardDescription>
      </CardHeader>
      <CardContent className='space-y-6'>
        {currentQuestion && (
          <>
            <div className='space-y-4'>
              <div>
                <Label className='text-base font-medium'>
                  {currentQuestion.text}
                  {currentQuestion.isRequired && (
                    <span className='text-red-500 ml-1'>*</span>
                  )}
                </Label>
                {currentQuestion.isRequired && (
                  <Badge variant='destructive' className='ml-2 text-xs'>
                    Required
                  </Badge>
                )}
              </div>

              {renderQuestionInput(currentQuestion)}

              {currentQuestion.isRequired && !canProceed() && (
                <Alert variant='destructive'>
                  <AlertTriangle className='h-4 w-4' />
                  <AlertDescription>
                    This field is required. Please provide an answer to
                    continue.
                  </AlertDescription>
                </Alert>
              )}
            </div>

            <div className='flex justify-between'>
              <Button
                onClick={handlePrevious}
                variant='outline'
                disabled={previewState.questionHistory.length === 0}
              >
                <ArrowLeft className='mr-2 h-4 w-4' />
                Previous
              </Button>

              <Button onClick={handleNext} disabled={!canProceed()}>
                {previewState.currentQuestionIndex ===
                sortedQuestions.length - 1
                  ? 'Complete'
                  : 'Next'}
                <ArrowRight className='ml-2 h-4 w-4' />
              </Button>
            </div>
          </>
        )}
      </CardContent>
    </Card>
  );
}
