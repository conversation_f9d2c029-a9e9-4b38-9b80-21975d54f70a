'use client';

import React from 'react';
import { ColumnDef } from '@tanstack/react-table';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { MoreHorizontal, Plus } from 'lucide-react';
import { User } from '@/types/account';
import { DataTable } from '@/components/ui/data-table/data-table';
import { DataTableColumnHeader } from '@/components/ui/data-table/data-table-column-header';
import type { DataTableConfig } from '@/components/ui/data-table/data-table';
import { useUsers } from '@/hooks/useUsers';

interface UserManagementTableProps {
  onEdit?: (user: User) => void;
  onCreateNew?: () => void;
  compact?: boolean;
  className?: string;
}

// Define filter configurations
const tableConfig: DataTableConfig = {
  searchColumn: 'name',
  searchPlaceholder: 'Filter users...',
  filters: [
    {
      id: 'status',
      title: 'Status',
      options: [
        { value: 'active', label: 'Active' },
        { value: 'inactive', label: 'Inactive' },
        { value: 'pending', label: 'Pending' },
      ],
    },
    {
      id: 'role',
      title: 'Role',
      options: [
        { value: 'Member', label: 'Member' },
        { value: 'Administrator', label: 'Administrator' },
        { value: 'Welon Trust', label: 'Welon Trust' },
        { value: 'Professional', label: 'Professional' },
      ],
    },
    {
      id: 'subrole',
      title: 'Subrole',
      options: [
        { value: 'Basic Member', label: 'Basic Member' },
        { value: 'Advanced', label: 'Advanced' },
        { value: 'Emergency Service', label: 'Emergency Service' },
        { value: 'Medical', label: 'Medical' },
        { value: 'Finance', label: 'Finance' },
        { value: 'Reporting', label: 'Reporting' },
      ],
    },
  ],
  enableColumnVisibility: true,
  enablePagination: true,
  enableRowSelection: false,
  defaultPageSize: 10,
};

export function UserManagementTable({
  onEdit,
  onCreateNew,
  compact = false,
  className = '',
}: UserManagementTableProps) {
  // Use the custom hook to fetch and manage user data
  const { users, loading, error, updateUserStatus, removeUser } = useUsers();

  // Handle user actions
  const handleActivate = async (user: User) => {
    try {
      await updateUserStatus(user.id, 'active');
    } catch (err) {
      console.error('Failed to activate user:', err);
    }
  };

  const handleDeactivate = async (user: User) => {
    try {
      await updateUserStatus(user.id, 'inactive');
    } catch (err) {
      console.error('Failed to deactivate user:', err);
    }
  };

  const handleDelete = async (user: User) => {
    try {
      await removeUser(user.id);
    } catch (err) {
      console.error('Failed to delete user:', err);
    }
  };
  // Define columns for the data table
  const columns: ColumnDef<User>[] = [
    {
      accessorKey: 'name',
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title='Name' />
      ),
      cell: ({ row }) => (
        <span className='font-medium'>{row.getValue('name')}</span>
      ),
    },
    {
      accessorKey: 'email',
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title='Email' />
      ),
      cell: ({ row }) => <span>{row.getValue('email')}</span>,
    },
    {
      accessorKey: 'role',
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title='Role' />
      ),
      cell: ({ row }) => <span>{row.getValue('role')}</span>,
      filterFn: (row, id, value) => {
        return value.includes(row.getValue(id));
      },
    },
    {
      accessorKey: 'subrole',
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title='Subrole' />
      ),
      cell: ({ row }) => (
        <span className='text-sm text-muted-foreground'>
          {row.getValue('subrole')}
        </span>
      ),
      filterFn: (row, id, value) => {
        return value.includes(row.getValue(id));
      },
    },
    {
      accessorKey: 'status',
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title='Status' />
      ),
      cell: ({ row }) => {
        const status = row.getValue('status') as string;
        if (status === 'active') {
          return (
            <Badge className='bg-green-600 text-white hover:bg-green-700'>
              Active
            </Badge>
          );
        } else if (status === 'inactive') {
          return (
            <Badge
              variant='outline'
              className='border-gray-300 text-[var(--custom-gray-medium)]'
            >
              Inactive
            </Badge>
          );
        } else {
          return (
            <Badge
              variant='outline'
              className='border-amber-500 text-amber-600'
            >
              Pending
            </Badge>
          );
        }
      },
      filterFn: (row, id, value) => {
        return value.includes(row.getValue(id));
      },
    },
    {
      accessorKey: 'assignedWelonTrust',
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title='Assigned Welon Trust' />
      ),
      cell: ({ row }) => {
        const user = row.original;
        if (user.role !== 'Member') {
          return <span className='text-sm text-muted-foreground'>N/A</span>;
        }

        if (!user.assignedWelonTrust) {
          return (
            <span className='text-sm text-muted-foreground'>Not assigned</span>
          );
        }

        const statusColor =
          user.assignedWelonTrust.status === 'active'
            ? 'text-green-600'
            : user.assignedWelonTrust.status === 'pending'
              ? 'text-amber-600'
              : 'text-red-600';

        return (
          <div className='flex flex-col gap-1'>
            <span className='text-sm font-medium'>
              {user.assignedWelonTrust.welonTrustName}
            </span>
            <span className={`text-xs capitalize ${statusColor}`}>
              {user.assignedWelonTrust.status}
            </span>
          </div>
        );
      },
    },
    {
      id: 'actions',
      header: 'Actions',
      cell: ({ row }) => {
        const user = row.original;

        return (
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant='ghost' className='h-8 w-8 p-0'>
                <span className='sr-only'>Open menu</span>
                <MoreHorizontal className='h-4 w-4' />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align='end'>
              {onEdit && (
                <DropdownMenuItem onClick={() => onEdit(user)}>
                  Edit
                </DropdownMenuItem>
              )}
              {user.status === 'active' && (
                <DropdownMenuItem
                  onClick={() => handleDeactivate(user)}
                  className='text-red-600'
                >
                  Deactivate
                </DropdownMenuItem>
              )}
              {user.status === 'inactive' && (
                <DropdownMenuItem
                  onClick={() => handleActivate(user)}
                  className='text-green-600'
                >
                  Activate
                </DropdownMenuItem>
              )}
              <DropdownMenuItem
                onClick={() => handleDelete(user)}
                className='text-red-600'
              >
                Delete
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        );
      },
    },
  ];

  return (
    <div className={`space-y-4 ${className}`}>
      {/* Header */}
      <div className='flex items-center justify-between'>
        <div>
          <h2 className='text-2xl font-bold tracking-tight'>User Management</h2>
        </div>
        {onCreateNew && (
          <Button onClick={onCreateNew} size={compact ? 'sm' : 'default'}>
            <Plus className='mr-2 h-4 w-4' />
            Create User
          </Button>
        )}
      </div>

      {/* Universal Data Table */}
      <DataTable
        columns={columns}
        data={users}
        config={tableConfig}
        loading={loading}
        error={error}
      />
    </div>
  );
}
