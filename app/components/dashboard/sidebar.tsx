'use client';

import React from 'react';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { cn } from '../../../lib/utils';
import {
  Home,
  FileText,
  Shield,
  AlertTriangle,
  BookOpen,
  CreditCard,
  Settings,
  Users,
  Heart,
  GraduationCap,
  Scale,
  Calendar,
  BarChart3,
  UserPlus,
  Share,
  Upload,
  Bell,
  MessageSquare,
} from 'lucide-react';
import { useRole } from '@/lib/roles/role-context';
import routes from '@/utils/routes';

interface SidebarProps {
  userRole?: 'Member' | 'Administrator' | 'Welon Trust' | 'Professional';
}

export function Sidebar({ userRole = 'Member' }: SidebarProps) {
  const pathname = usePathname();
  const { userContext } = useRole();

  // Simple test to ensure component renders
  console.log(
    'Sidebar rendering with role:',
    userRole,
    'context:',
    userContext
  );

  // Define navigation items based on user role and context
  const getNavItems = () => {
    // For linked accounts, show limited navigation
    if (userContext?.role === 'linked_account') {
      return [
        {
          title: 'Dashboard',
          href: '/linked',
          icon: <Home className='h-5 w-5' />,
          description: 'Linked account overview',
        },
        {
          title: 'Notifications',
          href: '/member/notifications',
          icon: <Bell className='h-5 w-5' />,
          description: 'View notifications',
        },
        {
          title: 'Shared Documents',
          href: '/dashboard/member/shared-documents',
          icon: <Share className='h-5 w-5' />,
          description: 'View shared documents',
        },
        {
          title: 'Emergency Access',
          href: '/emergency/documents',
          icon: <Shield className='h-5 w-5' />,
          description: 'Emergency document access',
        },
        {
          title: 'Emergency Contacts',
          href: '/dashboard/member/emergency-contacts',
          icon: <Users className='h-5 w-5' />,
          description: 'View emergency contacts',
        },
      ];
    }

    if (userRole === 'Member') {
      return [
        {
          title: 'Dashboard',
          href: '/dashboard',
          icon: <Home className='h-5 w-5' />,
          description: 'Overview',
        },
        {
          title: 'Notifications',
          href: '/member/notifications',
          icon: <Bell className='h-5 w-5' />,
          description: 'View notifications',
        },
        {
          title: 'Documents',
          href: '/documents',
          icon: <FileText className='h-5 w-5' />,
          description: 'Manage, review & sign documents',
        },
        {
          title: 'Living Documents',
          href: '/dashboard/member/living-documents',
          icon: <Heart className='h-5 w-5' />,
          description: 'Living will & directives',
        },
        {
          title: 'Shared Documents',
          href: '/dashboard/member/shared-documents',
          icon: <Share className='h-5 w-5' />,
          description: 'Joint estate planning',
        },
        {
          title: 'Emergency Contacts',
          href: '/dashboard/member/emergency-contacts',
          icon: <Shield className='h-5 w-5' />,
          description: 'Emergency contacts',
        },
        {
          title: "Dead Man's Switch",
          href: '/dashboard/member/deadmanswitch',
          icon: <AlertTriangle className='h-5 w-5' />,
          description: 'Automated notifications',
        },
        {
          title: 'Educational Content',
          href: '/dashboard/educational-content',
          icon: <GraduationCap className='h-5 w-5' />,
          description: 'Videos, articles & guides',
        },
        {
          title: 'Linked Accounts',
          href: '/dashboard/member/settings/linked-accounts',
          icon: <UserPlus className='h-5 w-5' />,
          description: 'Manage account links',
        },
        {
          title: 'Billing',
          href: '/dashboard/billing',
          icon: <CreditCard className='h-5 w-5' />,
          description: 'Subscription & payments',
        },
        {
          title: 'Settings',
          href: '/dashboard/settings',
          icon: <Settings className='h-5 w-5' />,
          description: 'Account settings',
        },
      ];
    }

    if (userRole === 'Administrator') {
      return [
        {
          title: 'Dashboard',
          href: routes.admin.dashboard,
          icon: <Home className='h-5 w-5' />,
          description: 'Admin overview',
        },
        {
          title: 'Notifications',
          href: routes.admin.notifications,
          icon: <Bell className='h-5 w-5' />,
          description: 'System notifications',
        },
        {
          title: 'Users',
          href: routes.admin.users,
          icon: <Users className='h-5 w-5' />,
          description: 'User management',
        },
        {
          title: 'Roles',
          href: routes.admin.roles,
          icon: <Shield className='h-5 w-5' />,
          description: 'Role & permission management',
        },
        {
          title: 'Content Management',
          href: routes.admin.content,
          icon: <BookOpen className='h-5 w-5' />,
          description: 'Educational content',
        },
        {
          title: 'Content Analytics',
          href: routes.admin.contentAnalytics,
          icon: <GraduationCap className='h-5 w-5' />,
          description: 'Content performance',
        },
        {
          title: 'Emergency Management',
          href: routes.admin.emergency,
          icon: <AlertTriangle className='h-5 w-5' />,
          description: 'DMS & emergency access',
        },
        {
          title: 'Templates',
          href: routes.admin.templates,
          icon: <FileText className='h-5 w-5' />,
          description: 'Document templates',
        },
        {
          title: 'Interview Builder',
          href: routes.admin.interviewBuilder,
          icon: <MessageSquare className='h-5 w-5' />,
          description: 'Create & manage interviews',
        },
        {
          title: 'Attorneys',
          href: routes.admin.attorneys,
          icon: <Scale className='h-5 w-5' />,
          description: 'Attorney management',
        },
        {
          title: 'Legal Updates',
          href: routes.admin.legalUpdates,
          icon: <AlertTriangle className='h-5 w-5' />,
          description: 'Legal changes tracking',
        },
        {
          title: 'Quarterly Review',
          href: routes.admin.quarterlyReview,
          icon: <Calendar className='h-5 w-5' />,
          description: 'Legal review signatures',
        },
        {
          title: 'Billing',
          href: routes.admin.billing,
          icon: <CreditCard className='h-5 w-5' />,
          description: 'Billing & subscriptions',
        },
        {
          title: 'Reports',
          href: routes.admin.reports,
          icon: <BarChart3 className='h-5 w-5' />,
          description: 'System reports',
        },
        {
          title: 'Settings',
          href: routes.admin.settings,
          icon: <Settings className='h-5 w-5' />,
          description: 'System settings',
        },
      ];
    }

    if (userRole === 'Welon Trust') {
      return [
        {
          title: 'Dashboard',
          href: routes.welon.dashboard,
          icon: <Home className='h-5 w-5' />,
          description: 'Welon Trust overview',
        },
        {
          title: 'Notifications',
          href: routes.welon.notifications,
          icon: <Bell className='h-5 w-5' />,
          description: 'View notifications',
        },
        {
          title: 'Document Management',
          href: routes.welon.documents,
          icon: <FileText className='h-5 w-5' />,
          description: 'View & manage all documents',
        },
        {
          title: 'Document Upload',
          href: routes.welon.uploadDocuments,
          icon: <Upload className='h-5 w-5' />,
          description: 'Upload signed documents',
        },
        {
          title: 'Emergency Documents',
          href: routes.welon.emergencyDocuments,
          icon: <AlertTriangle className='h-5 w-5' />,
          description: 'Emergency access',
        },
        {
          title: 'Submit Evidence',
          href: routes.welon.submitEvidence,
          icon: <Shield className='h-5 w-5' />,
          description: 'Evidence submission',
        },
      ];
    }

    return [];
  };

  const navItems = getNavItems();

  const getActiveItemHref = () => {
    const matchingItems = navItems.filter(
      item => pathname === item.href || pathname.startsWith(`${item.href}/`)
    );

    if (matchingItems.length === 0) return null;
    if (matchingItems.length === 1) return matchingItems[0].href;

    const bestMatch = matchingItems.reduce((longest, current) =>
      current.href.length > longest.href.length ? current : longest
    );

    return bestMatch.href;
  };

  const activeItemHref = getActiveItemHref();

  // Get display title based on role context
  const getDisplayTitle = () => {
    if (userContext?.role === 'linked_account') {
      return `${userContext.displayName}`;
    }
    return `${userRole} Dashboard`;
  };

  return (
    <aside className='w-64 bg-background border-r border-gray-200 min-h-screen shadow-sm'>
      <div className='p-6'>
        <h2 className='text-lg font-semibold mb-6'>{getDisplayTitle()}</h2>

        <nav className='space-y-2'>
          {navItems.map(item => {
            const isActive = activeItemHref === item.href;

            return (
              <Link
                key={item.href}
                href={item.href}
                className={cn(
                  'flex items-center px-3 py-2 text-sm font-medium rounded-lg transition-colors group',
                  isActive
                    ? 'bg-[var(--eggplant)]/10 text-[var(--eggplant)] border-l-4 border-[var(--eggplant)]'
                    : 'hover:bg-gray-100 hover:text-[var(--eggplant)]'
                )}
              >
                <span
                  className={cn(
                    'mr-3',
                    isActive
                      ? 'text-[var(--eggplant)]'
                      : 'text-[var(--custom-gray-dark)] group-hover:text-[var(--eggplant)]'
                  )}
                >
                  {item.icon}
                </span>
                <div className='flex-1'>
                  <div className='font-medium'>{item.title}</div>
                  <div
                    className={cn(
                      'text-xs mt-0.5',
                      isActive
                        ? 'text-[var(--eggplant)]/70'
                        : 'text-[var(--custom-gray-dark)] group-hover:text-[var(--eggplant)]/70'
                    )}
                  >
                    {item.description}
                  </div>
                </div>
              </Link>
            );
          })}
        </nav>
      </div>
    </aside>
  );
}
