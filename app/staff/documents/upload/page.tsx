'use client';

import React, { useState, useCallback } from 'react';
import { useRouter } from 'next/navigation';
import { But<PERSON> } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Checkbox } from '@/components/ui/checkbox';
import { Headline, Subhead } from '../../../../components/ui/brand/typography';
import {
  Upload,
  FileText,
  CheckCircle,
  AlertCircle,
  X,
  Eye,
  Download,
  User,
  Calendar,
  FileCheck,
} from 'lucide-react';

// Types for document upload
interface UploadedDocument {
  id: string;
  file: File;
  preview?: string;
  memberId: string;
  memberName: string;
  documentType: string;
  status: 'pending' | 'approved' | 'rejected';
  validationErrors: string[];
}

interface ValidationResult {
  isValid: boolean;
  errors: string[];
  warnings: string[];
}

export default function StaffDocumentUploadPage() {
  const router = useRouter();
  const [dragActive, setDragActive] = useState(false);
  const [uploadedFiles, setUploadedFiles] = useState<UploadedDocument[]>([]);
  const [currentUpload, setCurrentUpload] = useState<{
    file: File | null;
    memberId: string;
    memberName: string;
    documentType: string;
    notes: string;
    validationChecks: {
      signatures: boolean;
      notarization: boolean;
      witnesses: boolean;
      legibility: boolean;
    };
  }>({
    file: null,
    memberId: '',
    memberName: '',
    documentType: '',
    notes: '',
    validationChecks: {
      signatures: false,
      notarization: false,
      witnesses: false,
      legibility: false,
    },
  });
  const [isUploading, setIsUploading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);

  // Handle drag events
  const handleDrag = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    if (e.type === 'dragenter' || e.type === 'dragover') {
      setDragActive(true);
    } else if (e.type === 'dragleave') {
      setDragActive(false);
    }
  }, []);

  // Handle drop
  const handleDrop = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setDragActive(false);

    if (e.dataTransfer.files && e.dataTransfer.files[0]) {
      handleFileSelect(e.dataTransfer.files[0]);
    }
  }, []);

  // Handle file selection
  const handleFileSelect = (file: File) => {
    // Validate file type
    const allowedTypes = [
      'application/pdf',
      'image/jpeg',
      'image/png',
      'image/jpg',
    ];
    if (!allowedTypes.includes(file.type)) {
      setError(
        'Invalid file type. Please upload a PDF or image file (JPEG, PNG).'
      );
      return;
    }

    // Validate file size (max 10MB)
    if (file.size > 10 * 1024 * 1024) {
      setError('File size exceeds 10MB limit. Please select a smaller file.');
      return;
    }

    setCurrentUpload(prev => ({ ...prev, file }));
    setError(null);
  };

  // Handle file input change
  const handleFileInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      handleFileSelect(e.target.files[0]);
    }
  };

  // Validate document
  const validateDocument = (): ValidationResult => {
    const errors: string[] = [];
    const warnings: string[] = [];

    if (!currentUpload.file) {
      errors.push('No file selected');
    }

    if (!currentUpload.memberId.trim()) {
      errors.push('Member ID is required');
    }

    if (!currentUpload.memberName.trim()) {
      errors.push('Member name is required');
    }

    if (!currentUpload.documentType) {
      errors.push('Document type is required');
    }

    // Check validation requirements
    const checks = currentUpload.validationChecks;
    if (!checks.signatures) {
      errors.push('Document signatures must be verified');
    }

    if (!checks.legibility) {
      errors.push('Document legibility must be verified');
    }

    // Conditional checks based on document type
    if (currentUpload.documentType === 'will' && !checks.witnesses) {
      errors.push('Witness signatures must be verified for wills');
    }

    if (
      (currentUpload.documentType === 'will' ||
        currentUpload.documentType === 'poa') &&
      !checks.notarization
    ) {
      errors.push('Notarization must be verified for this document type');
    }

    return {
      isValid: errors.length === 0,
      errors,
      warnings,
    };
  };

  // Handle upload
  const handleUpload = async () => {
    const validation = validateDocument();

    if (!validation.isValid) {
      setError(validation.errors.join(', '));
      return;
    }

    setIsUploading(true);
    setError(null);

    try {
      // Simulate upload process
      await new Promise(resolve => setTimeout(resolve, 2000));

      // In a real implementation, this would:
      // 1. Upload file to S3
      // 2. Store metadata in DynamoDB
      // 3. Send notification to member
      // 4. Update document status

      const newDocument: UploadedDocument = {
        id: `doc-${Date.now()}`,
        file: currentUpload.file!,
        memberId: currentUpload.memberId,
        memberName: currentUpload.memberName,
        documentType: currentUpload.documentType,
        status: 'approved',
        validationErrors: [],
      };

      setUploadedFiles(prev => [newDocument, ...prev]);
      setSuccess(
        `Document uploaded successfully for ${currentUpload.memberName}`
      );

      // Reset form
      setCurrentUpload({
        file: null,
        memberId: '',
        memberName: '',
        documentType: '',
        notes: '',
        validationChecks: {
          signatures: false,
          notarization: false,
          witnesses: false,
          legibility: false,
        },
      });

      // Clear success message after 5 seconds
      setTimeout(() => setSuccess(null), 5000);
    } catch (err) {
      setError('Failed to upload document. Please try again.');
    } finally {
      setIsUploading(false);
    }
  };

  // Handle rejection
  const handleRejectDocument = () => {
    if (!currentUpload.notes.trim()) {
      setError('Please provide rejection reason in notes');
      return;
    }

    // In a real implementation, this would:
    // 1. Mark document as rejected
    // 2. Send notification to member with rejection reason
    // 3. Log the rejection

    alert(
      `Document rejected. Member will be notified with reason: ${currentUpload.notes}`
    );

    // Reset form
    setCurrentUpload({
      file: null,
      memberId: '',
      memberName: '',
      documentType: '',
      notes: '',
      validationChecks: {
        signatures: false,
        notarization: false,
        witnesses: false,
        legibility: false,
      },
    });
  };

  const allRequiredChecks =
    currentUpload.validationChecks.signatures &&
    currentUpload.validationChecks.legibility &&
    (currentUpload.documentType !== 'will' ||
      currentUpload.validationChecks.witnesses) &&
    (!['will', 'poa'].includes(currentUpload.documentType) ||
      currentUpload.validationChecks.notarization);

  return (
    <div className='container mx-auto py-8 px-4'>
      <div className='max-w-4xl mx-auto'>
        <div className='mb-8'>
          <Headline className='mb-2'>Document Upload - Welon Staff</Headline>
          <Subhead className='text-muted-foreground'>
            Upload and validate signed documents received from members
          </Subhead>
        </div>

        {/* Success/Error Messages */}
        {success && (
          <Alert className='mb-6 border-green-200 bg-green-50'>
            <CheckCircle className='h-4 w-4 text-green-600' />
            <AlertTitle className='text-green-800'>Success</AlertTitle>
            <AlertDescription className='text-green-700'>
              {success}
            </AlertDescription>
          </Alert>
        )}

        {error && (
          <Alert variant='destructive' className='mb-6'>
            <AlertCircle className='h-4 w-4' />
            <AlertTitle>Error</AlertTitle>
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}

        {/* Upload Form */}
        <Card className='mb-8'>
          <CardHeader>
            <CardTitle>Upload Document</CardTitle>
            <CardDescription>
              Upload signed documents and validate them before approval
            </CardDescription>
          </CardHeader>
          <CardContent className='space-y-6'>
            {/* File Upload Area */}
            <div
              className={`border-2 border-dashed rounded-lg p-8 text-center transition-colors ${
                dragActive ? 'border-blue-500 bg-blue-50' : 'border-gray-300'
              }`}
              onDragEnter={handleDrag}
              onDragLeave={handleDrag}
              onDragOver={handleDrag}
              onDrop={handleDrop}
            >
              {currentUpload.file ? (
                <div className='space-y-4'>
                  <div className='flex items-center justify-center gap-3'>
                    <FileText className='h-8 w-8 text-blue-600' />
                    <div>
                      <p className='font-medium'>{currentUpload.file.name}</p>
                      <p className='text-sm text-muted-foreground'>
                        {(currentUpload.file.size / 1024 / 1024).toFixed(2)} MB
                      </p>
                    </div>
                    <Button
                      variant='outline'
                      size='sm'
                      onClick={() =>
                        setCurrentUpload(prev => ({ ...prev, file: null }))
                      }
                    >
                      <X className='h-4 w-4' />
                    </Button>
                  </div>
                </div>
              ) : (
                <div className='space-y-4'>
                  <Upload className='h-12 w-12 mx-auto text-muted-foreground' />
                  <div>
                    <p className='text-lg font-medium'>
                      Drop files here or click to browse
                    </p>
                    <p className='text-sm text-muted-foreground'>
                      Accepted formats: PDF, JPEG, PNG. Maximum size: 10MB.
                    </p>
                  </div>
                  <Input
                    type='file'
                    className='hidden'
                    id='fileInput'
                    accept='.pdf,.jpg,.jpeg,.png'
                    onChange={handleFileInputChange}
                  />
                  <Button
                    variant='outline'
                    onClick={() =>
                      document.getElementById('fileInput')?.click()
                    }
                  >
                    Select File
                  </Button>
                </div>
              )}
            </div>

            {/* Member Information */}
            <div className='grid grid-cols-1 md:grid-cols-2 gap-4'>
              <div className='space-y-2'>
                <Label htmlFor='memberId'>Member ID</Label>
                <Input
                  id='memberId'
                  value={currentUpload.memberId}
                  onChange={e =>
                    setCurrentUpload(prev => ({
                      ...prev,
                      memberId: e.target.value,
                    }))
                  }
                  placeholder='e.g., 10001'
                />
              </div>
              <div className='space-y-2'>
                <Label htmlFor='memberName'>Member Name</Label>
                <Input
                  id='memberName'
                  value={currentUpload.memberName}
                  onChange={e =>
                    setCurrentUpload(prev => ({
                      ...prev,
                      memberName: e.target.value,
                    }))
                  }
                  placeholder='e.g., John Smith'
                />
              </div>
            </div>

            {/* Document Type */}
            <div className='space-y-2'>
              <Label htmlFor='documentType'>Document Type</Label>
              <Select
                value={currentUpload.documentType}
                onValueChange={value =>
                  setCurrentUpload(prev => ({ ...prev, documentType: value }))
                }
              >
                <SelectTrigger>
                  <SelectValue placeholder='Select document type' />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value='will'>Last Will and Testament</SelectItem>
                  <SelectItem value='trust'>Revocable Living Trust</SelectItem>
                  <SelectItem value='poa'>Power of Attorney</SelectItem>
                  <SelectItem value='healthcare_directive'>
                    Healthcare Directive
                  </SelectItem>
                </SelectContent>
              </Select>
            </div>

            {/* Validation Checklist */}
            <div className='space-y-4'>
              <Label>Validation Checklist</Label>
              <div className='space-y-3 p-4 bg-gray-50 rounded-lg'>
                <div className='flex items-center space-x-3'>
                  <Checkbox
                    id='signatures'
                    checked={currentUpload.validationChecks.signatures}
                    onCheckedChange={checked =>
                      setCurrentUpload(prev => ({
                        ...prev,
                        validationChecks: {
                          ...prev.validationChecks,
                          signatures: !!checked,
                        },
                      }))
                    }
                  />
                  <Label htmlFor='signatures' className='text-sm'>
                    All required signatures are present and legible
                  </Label>
                </div>

                <div className='flex items-center space-x-3'>
                  <Checkbox
                    id='notarization'
                    checked={currentUpload.validationChecks.notarization}
                    onCheckedChange={checked =>
                      setCurrentUpload(prev => ({
                        ...prev,
                        validationChecks: {
                          ...prev.validationChecks,
                          notarization: !!checked,
                        },
                      }))
                    }
                  />
                  <Label htmlFor='notarization' className='text-sm'>
                    Notarization is complete and valid (if required)
                  </Label>
                </div>

                <div className='flex items-center space-x-3'>
                  <Checkbox
                    id='witnesses'
                    checked={currentUpload.validationChecks.witnesses}
                    onCheckedChange={checked =>
                      setCurrentUpload(prev => ({
                        ...prev,
                        validationChecks: {
                          ...prev.validationChecks,
                          witnesses: !!checked,
                        },
                      }))
                    }
                  />
                  <Label htmlFor='witnesses' className='text-sm'>
                    Witness signatures are present and valid (if required)
                  </Label>
                </div>

                <div className='flex items-center space-x-3'>
                  <Checkbox
                    id='legibility'
                    checked={currentUpload.validationChecks.legibility}
                    onCheckedChange={checked =>
                      setCurrentUpload(prev => ({
                        ...prev,
                        validationChecks: {
                          ...prev.validationChecks,
                          legibility: !!checked,
                        },
                      }))
                    }
                  />
                  <Label htmlFor='legibility' className='text-sm'>
                    Document is clear, legible, and complete
                  </Label>
                </div>
              </div>
            </div>

            {/* Notes */}
            <div className='space-y-2'>
              <Label htmlFor='notes'>Notes (Optional)</Label>
              <Textarea
                id='notes'
                value={currentUpload.notes}
                onChange={e =>
                  setCurrentUpload(prev => ({ ...prev, notes: e.target.value }))
                }
                placeholder='Add any notes about the document or validation process...'
                rows={3}
              />
            </div>

            {/* Action Buttons */}
            <div className='flex gap-4'>
              <Button
                onClick={handleUpload}
                disabled={
                  !currentUpload.file || !allRequiredChecks || isUploading
                }
                className='flex-1 bg-green-600 hover:bg-green-700'
              >
                {isUploading ? (
                  <>
                    <Upload className='h-4 w-4 mr-2 animate-pulse' />
                    Uploading...
                  </>
                ) : (
                  <>
                    <CheckCircle className='h-4 w-4 mr-2' />
                    Approve & Upload
                  </>
                )}
              </Button>

              <Button
                variant='destructive'
                onClick={handleRejectDocument}
                disabled={!currentUpload.file || isUploading}
                className='flex-1'
              >
                <X className='h-4 w-4 mr-2' />
                Reject Document
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* Recent Uploads */}
        {uploadedFiles.length > 0 && (
          <Card>
            <CardHeader>
              <CardTitle>Recent Uploads</CardTitle>
              <CardDescription>Recently processed documents</CardDescription>
            </CardHeader>
            <CardContent>
              <div className='space-y-3'>
                {uploadedFiles.slice(0, 5).map(doc => (
                  <div
                    key={doc.id}
                    className='flex items-center justify-between p-3 bg-gray-50 rounded-lg'
                  >
                    <div className='flex items-center gap-3'>
                      <FileText className='h-5 w-5 text-blue-600' />
                      <div>
                        <p className='font-medium'>{doc.file.name}</p>
                        <p className='text-sm text-muted-foreground'>
                          {doc.memberName} (ID: {doc.memberId}) •{' '}
                          {doc.documentType}
                        </p>
                      </div>
                    </div>
                    <div className='flex items-center gap-2'>
                      <Badge
                        variant={
                          doc.status === 'approved' ? 'default' : 'destructive'
                        }
                        className={
                          doc.status === 'approved'
                            ? 'bg-green-100 text-green-800'
                            : ''
                        }
                      >
                        {doc.status === 'approved' ? 'Approved' : 'Rejected'}
                      </Badge>
                      <span className='text-xs text-muted-foreground'>
                        {new Date().toLocaleDateString()}
                      </span>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        )}
      </div>
    </div>
  );
}
