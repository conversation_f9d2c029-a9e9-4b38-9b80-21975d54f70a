'use client';

import React from 'react';
import { AdminGuard } from '@/lib/auth/admin-guard';
import { Sidebar } from '@/components/dashboard/sidebar';
import { AdminContainer } from '../../components/ui/container';
import {
  UserProvider,
  useUserContext,
} from '@/components/welon-trust/user-context';
import { UserSelector } from '@/components/welon-trust/user-selector';
import { UserBreadcrumb } from '@/components/welon-trust/user-breadcrumb';
import { Card, CardContent } from '@/components/ui/card';

function StaffLayoutContent({ children }: { children: React.ReactNode }) {
  const { selectedUser, setSelectedUser, availableUsers, isLoading } =
    useUserContext();

  return (
    <AdminGuard requiredRole='ADMINS'>
      <div className='min-h-screen flex bg-background'>
        <Sidebar userRole='Welon Trust' />
        <div className='flex-1'>
          <main>
            <AdminContainer>
              {/* User Selection Header */}
              <Card className='mb-6 border-blue-200 bg-gradient-to-r from-blue-50 to-indigo-50'>
                <CardContent className='p-6'>
                  <div className='max-w-md'>
                    <UserSelector
                      selectedUser={selectedUser}
                      onUserSelect={setSelectedUser}
                      users={availableUsers}
                    />
                  </div>
                </CardContent>
              </Card>

              {/* User Context Breadcrumb */}
              <UserBreadcrumb />

              {/* Main Content */}
              {children}
            </AdminContainer>
          </main>
        </div>
      </div>
    </AdminGuard>
  );
}

export default function StaffLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <UserProvider>
      <StaffLayoutContent>{children}</StaffLayoutContent>
    </UserProvider>
  );
}
