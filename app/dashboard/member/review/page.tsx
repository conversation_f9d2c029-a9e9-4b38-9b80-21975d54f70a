'use client';

import React, { useState } from 'react';
import { useRouter } from 'next/navigation';
import { Button } from '@/components/ui/button';
import {
  InterviewProvider,
  useInterview,
  Question,
} from '@/components/interview/interview-context';

// Component to display a section of responses
const ResponseSection: React.FC<{
  title: string;
  questions: Question[];
  responses: Record<string, any>;
  onEdit: (questionId: string) => void;
}> = ({ title, questions, responses, onEdit }) => {
  return (
    <div className='mb-8'>
      <h2 className='text-xl font-bold text-black-6c mb-4 border-b pb-2'>
        {title}
      </h2>
      <div className='space-y-4'>
        {questions.map(question => {
          const response = responses[question.id]?.value;
          if (!response) return null;

          return (
            <div key={question.id} className='flex justify-between items-start'>
              <div>
                <p className='font-medium'>{question.questionTitle}</p>
                <p className='text-[var(--custom-gray-medium)]'>{response}</p>
              </div>
              <Button
                variant='outline'
                size='sm'
                onClick={() => onEdit(question.id)}
                className='ml-4'
              >
                Edit
              </Button>
            </div>
          );
        })}
      </div>
    </div>
  );
};

// Document preview component
const DocumentPreview: React.FC<{ responses: Record<string, any> }> = ({
  responses,
}) => {
  const name = responses['name']?.value || '____________________';
  const address = responses['address']?.value || '____________________';
  const executor = responses['executor']?.value || '____________________';
  const homeBeneficiary =
    responses['home_beneficiary']?.value || '____________________';
  const bankBeneficiary =
    responses['bank_beneficiary']?.value || '____________________';
  const investmentBeneficiary =
    responses['investment_beneficiary']?.value || '____________________';

  return (
    <div className='border p-6 rounded-md bg-background shadow-sm'>
      <h3 className='text-center text-xl font-bold mb-6'>
        LAST WILL AND TESTAMENT
      </h3>

      <p className='mb-4'>
        I, {name}, residing at {address}, being of sound mind, do hereby make,
        publish, and declare this to be my Last Will and Testament, hereby
        revoking all wills and codicils previously made by me.
      </p>

      <p className='mb-4'>
        I hereby nominate and appoint {executor} to be the Executor of this, my
        Last Will and Testament.
      </p>

      <h4 className='font-bold mt-6 mb-2'>
        ARTICLE I: DISTRIBUTION OF PROPERTY
      </h4>

      {responses['own_home']?.value === 'Yes' && (
        <p className='mb-2'>
          I give, devise, and bequeath my real property located at{' '}
          {responses['home_address']?.value || '____________________'} to{' '}
          {homeBeneficiary}.
        </p>
      )}

      {responses['bank_accounts']?.value === 'Yes' && (
        <p className='mb-2'>
          I give, devise, and bequeath all of my bank accounts to{' '}
          {bankBeneficiary}.
        </p>
      )}

      {responses['investments']?.value === 'Yes' && (
        <p className='mb-2'>
          I give, devise, and bequeath all of my investment accounts to{' '}
          {investmentBeneficiary}.
        </p>
      )}

      <p className='mt-8 mb-8'>
        IN WITNESS WHEREOF, I have hereunto set my hand this _____ day of
        ____________, 20___.
      </p>

      <div className='mt-12 border-t pt-4'>
        <p className='mb-8'>____________________________</p>
        <p>{name}, Testator</p>
      </div>
    </div>
  );
};

const ReviewContent: React.FC = () => {
  const { questions, responses, goToQuestion } = useInterview();
  const router = useRouter();
  const [showPreview, setShowPreview] = useState(false);

  // Since the database schema doesn't have category field, show all questions together
  const allQuestions = questions;

  const handleEdit = (questionId: string) => {
    goToQuestion(questionId);
    router.push('/member/interview');
  };

  const handleFinalize = () => {
    alert(
      'Your estate plan has been finalized! In a real implementation, this would generate legal documents and store them securely.'
    );
    router.push('/dashboard');
  };

  return (
    <div className='container mx-auto py-8 px-4'>
      <div className='max-w-4xl mx-auto'>
        <div className='mb-8'>
          <h1 className='text-3xl font-bold text-black-6c mb-2'>
            Review Your Information
          </h1>
          <p className='text-[var(--custom-gray-medium)]'>
            Please review the information you've provided. You can edit any
            section by clicking the "Edit" button.
          </p>
        </div>

        <div className='mb-8 flex justify-end space-x-4'>
          <Button
            variant='outline'
            onClick={() => setShowPreview(!showPreview)}
          >
            {showPreview ? 'Hide Document Preview' : 'Show Document Preview'}
          </Button>

          <Button onClick={() => router.push('/member/interview')}>
            Continue Interview
          </Button>
        </div>

        {showPreview ? (
          <div className='mb-8'>
            <h2 className='text-2xl font-bold text-black-6c mb-4'>
              Document Preview
            </h2>
            <DocumentPreview responses={responses} />
            <div className='mt-4 flex justify-end'>
              <Button onClick={() => setShowPreview(false)} variant='outline'>
                Back to Review
              </Button>
            </div>
          </div>
        ) : (
          <>
            <ResponseSection
              title='Your Responses'
              questions={allQuestions}
              responses={responses}
              onEdit={handleEdit}
            />

            <div className='mt-12 flex justify-between'>
              <Button
                variant='outline'
                onClick={() => router.push('/member/interview')}
              >
                Back to Interview
              </Button>

              <Button onClick={handleFinalize}>Finalize Documents</Button>
            </div>
          </>
        )}
      </div>
    </div>
  );
};

export default function ReviewPage() {
  return (
    <InterviewProvider>
      <ReviewContent />
    </InterviewProvider>
  );
}
