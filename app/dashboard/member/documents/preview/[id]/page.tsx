import React from 'react';
import { DocumentPreviewClient } from './document-preview-client';
import { getTemplateServer } from '@/app/utils/templates-server';
import { notFound } from 'next/navigation';

export default async function DocumentPreviewPage({
  params,
}: {
  params: Promise<{ id: string }>;
}) {
  const { id: templateId } = await params;

  try {
    // Fetch template data from database
    const { template, latestVersion } = await getTemplateServer(templateId);

    if (!template) {
      console.error(`Template not found with ID: ${templateId}`);
      notFound();
    }

    // Extract document information from template
    const documentType = template.type || 'Legal Document';
    const documentState = template.templateState || 'Unknown State';
    const templateName =
      template.templateName || `${documentState} ${documentType}`;
    const templateContent = latestVersion?.content || '';

    return (
      <DocumentPreviewClient
        templateId={templateId}
        documentType={documentType}
        documentState={documentState}
        templateName={templateName}
        templateContent={templateContent}
        template={template}
      />
    );
  } catch (error) {
    console.error('Error fetching template:', error);

    // If template not found, show 404
    if (error instanceof Error && error.message.includes('not found')) {
      notFound();
    }

    // For other errors, you might want to show an error page
    // For now, we'll fall back to mock data with error indication
    return (
      <DocumentPreviewClient
        templateId={templateId}
        documentType='Error Loading Template'
        documentState='Unknown'
        templateName='Template Load Error'
        templateContent=''
        template={null}
        error={
          error instanceof Error ? error.message : 'Unknown error occurred'
        }
      />
    );
  }
}
