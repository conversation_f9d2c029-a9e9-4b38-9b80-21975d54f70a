'use client';

import React, { useState, useMemo } from 'react';
import { useRouter } from 'next/navigation';
import { But<PERSON> } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Checkbox } from '@/components/ui/checkbox';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import {
  AlertCircle,
  CheckCircle2,
  FileText,
  Calendar,
  User,
} from 'lucide-react';
import type { Schema } from '@/amplify/data/resource';
import { useQuery } from '@tanstack/react-query';
import { useAuth } from '@/context/AuthContext';
import { fetchUserAttributes } from 'aws-amplify/auth';
import { createDocument, CreateDocumentData } from '@/lib/api/documents';
import { toast } from 'sonner';

type Template = Schema['Template']['type'];

// Determine a document type based on documentType string
const getDocumentType = (
  docType: string
): 'Will' | 'Trust' | 'POA' | 'Other' => {
  const lowerType = docType.toLowerCase();
  if (lowerType.includes('will')) return 'Will';
  if (lowerType.includes('trust')) return 'Trust';
  if (lowerType.includes('poa') || lowerType.includes('power of attorney'))
    return 'POA';
  return 'Other';
};

interface DocumentPreviewClientProps {
  templateId: string;
  documentType: string;
  documentState: string;
  templateName: string;
  templateContent: string;
  template: Template | null;
  error?: string;
}

export function DocumentPreviewClient({
  templateId,
  documentType,
  documentState,
  templateName,
  templateContent,
  template,
  error: initialError,
}: DocumentPreviewClientProps) {
  const { user, userId } = useAuth();
  const router = useRouter();
  const [approved, setApproved] = useState(false);
  const [error, setError] = useState<string | null>(initialError || null);
  const [success, setSuccess] = useState<string | null>(null);
  const [isSaving, setIsSaving] = useState<boolean>(false);

  const { data: userAttributes, isLoading: attributesLoading } = useQuery({
    queryKey: ['user-attributes'],
    queryFn: fetchUserAttributes,
    enabled: !!user && !!userId,
  });

  // Template population logic
  const populatedContent = useMemo(() => {
    if (!templateContent || !userAttributes) {
      return templateContent;
    }

    // Create a mapping of placeholders to user attribute values
    // This mapping aligns with the registration data structure:
    // userAttributes: { email, given_name, family_name, address (stateLabel), birthdate (formatted), phone_number (formatted), gender }
    const placeholderMap: Record<string, string> = {
      // Member information - using given_name and family_name from registration
      '[Member_NAME]':
        `${userAttributes.given_name || ''} ${userAttributes.family_name || ''}`.trim() ||
        'N/A',
      '[MEMBER_NAME]':
        `${userAttributes.given_name || ''} ${userAttributes.family_name || ''}`.trim() ||
        'N/A',
      '[Member_FIRST_NAME]': userAttributes.given_name || 'N/A',
      '[Member_LAST_NAME]': userAttributes.family_name || 'N/A',
      '[FIRST_NAME]': userAttributes.given_name || 'N/A',
      '[LAST_NAME]': userAttributes.family_name || 'N/A',

      // Contact information - using email and phone_number (formatted) from registration
      '[Member_EMAIL]': userAttributes.email || 'N/A',
      '[EMAIL]': userAttributes.email || 'N/A',
      '[Member_PHONE]': userAttributes.phone_number || 'N/A',
      '[PHONE_NUMBER]': userAttributes.phone_number || 'N/A',

      // Address/State information - address contains stateLabel from registration
      '[Member_ADDRESS]': userAttributes.address || 'N/A',
      '[ADDRESS]': userAttributes.address || 'N/A',
      '[Member_STATE]': userAttributes.address || 'N/A', // address field contains state from registration
      '[STATE_LABEL]': userAttributes.address || 'N/A',

      // Personal information - using birthdate (formatted) and gender from registration
      '[Member_DOB]': userAttributes.birthdate || 'N/A',
      '[DATE_OF_BIRTH]': userAttributes.birthdate || 'N/A',
      '[BIRTHDATE]': userAttributes.birthdate || 'N/A',
      '[Member_GENDER]': userAttributes.gender || 'N/A',
      '[GENDER]': userAttributes.gender || 'N/A',

      // Document metadata
      '[TODAY_DATE]': new Date().toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'long',
        day: 'numeric',
      }),
      '[CURRENT_DATE]': new Date().toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'long',
        day: 'numeric',
      }),
      '[DOCUMENT_TYPE]': documentType || 'N/A',
      '[DOCUMENT_STATE]': documentState || 'N/A',
      '[STATE]': documentState || 'N/A',

      // User ID for reference
      '[USER_ID]': user?.userId || 'N/A',
      '[MEMBER_ID]': user?.userId || 'N/A',
    };

    // Replace all placeholders in the template content
    let populatedTemplate = templateContent;

    Object.entries(placeholderMap).forEach(([placeholder, value]) => {
      // Create a regex that matches the placeholder (case-insensitive and global)
      const regex = new RegExp(
        placeholder.replace(/[.*+?^${}()|[\]\\]/g, '\\$&'),
        'gi'
      );
      populatedTemplate = populatedTemplate.replace(regex, value);
    });

    return populatedTemplate;
  }, [
    templateContent,
    userAttributes,
    documentType,
    documentState,
    user?.userId,
  ]);

  const handleApproveChange = (checked: boolean) => {
    setApproved(checked);
    setError(null);
  };

  const handleEdit = () => {
    // In a real implementation, this would navigate to the specific section that needs editing
    router.push('/member/interview');
  };

  const handleSave = async () => {
    setIsSaving(true);

    try {
      if (!approved) {
        setError('Please approve the document before saving');
        return;
      }

      if (!user?.userId) {
        setError('User not authenticated');
        return;
      }

      // Create document data
      const documentData: CreateDocumentData = {
        title: templateName || `${documentType} for ${documentState}`,
        type: getDocumentType(documentType),
        status: 'draft',
        version: '1.0',
        content: populatedContent || templateContent || '',
        templateId: templateId,
        templateContent: populatedContent || templateContent,
        documentState: documentState,
      };

      // Clear any previous errors
      setError(null);

      await createDocument(documentData, userId);
      toast.success('Document saved to your account');
      router.push('/dashboard');
    } catch (error) {
      console.error('Error creating document:', error);
      setError('Failed to save document');
      toast.error('Failed to save document');
    } finally {
      setIsSaving(false);
    }
  };

  return (
    <div className='container mx-auto py-8 px-4'>
      <div className='max-w-4xl mx-auto'>
        <div className='mb-8'>
          <h1 className='text-3xl font-bold text-black-6c mb-2'>
            Document Preview
          </h1>
          <p className='text-[var(--custom-gray-medium)]'>
            Review your {documentType} for {documentState}. Please check all
            details carefully before approving.
          </p>

          {/* Template Information */}
          {template && (
            <div className='mt-4 p-4 bg-blue-50 rounded-lg border border-blue-200'>
              <div className='flex items-center gap-4 text-sm text-blue-800'>
                <div className='flex items-center gap-1'>
                  <FileText className='h-4 w-4' />
                  <span className='font-medium'>{templateName}</span>
                </div>
                {template.createdAt && (
                  <div className='flex items-center gap-1'>
                    <Calendar className='h-4 w-4' />
                    <span>
                      Created:{' '}
                      {new Date(template.createdAt).toLocaleDateString()}
                    </span>
                  </div>
                )}
              </div>
            </div>
          )}
        </div>

        {success && (
          <Alert className='mb-6 bg-green-50 text-green-800 border-green-200'>
            <CheckCircle2 className='h-4 w-4' />
            <AlertTitle>Success</AlertTitle>
            <AlertDescription>{success}</AlertDescription>
          </Alert>
        )}

        {error && (
          <Alert variant='destructive' className='mb-6'>
            <AlertCircle className='h-4 w-4' />
            <AlertTitle>Error</AlertTitle>
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}

        <Card className='mb-8'>
          <CardHeader>
            <CardTitle>Document Preview</CardTitle>
          </CardHeader>
          <CardContent>
            <div className='border p-6 rounded-md bg-background shadow-sm relative'>
              {/* Watermark */}
              <div className='absolute inset-0 flex items-center justify-center opacity-20 rotate-45 text-red-500 text-6xl font-bold pointer-events-none'>
                DRAFT
              </div>

              {/* Document content */}
              <div className='relative z-10'>
                {attributesLoading ? (
                  // Loading state while fetching user attributes
                  <div className='text-center text-[var(--custom-gray-medium)] py-8'>
                    <div className='animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900 mx-auto mb-4'></div>
                    <p>Loading user information...</p>
                  </div>
                ) : populatedContent ? (
                  // Render populated template content with user data
                  <div
                    className='prose max-w-none'
                    dangerouslySetInnerHTML={{ __html: populatedContent }}
                  />
                ) : (
                  // Fallback content when no template content is available
                  <>
                    <h3 className='text-center text-xl font-bold mb-6'>
                      {documentType.toUpperCase()}
                    </h3>

                    <div className='text-center text-[var(--custom-gray-medium)] py-8'>
                      <FileText className='h-16 w-16 mx-auto mb-4 opacity-50' />
                      <p className='text-lg mb-2'>
                        Template content not available
                      </p>
                      <p className='text-sm'>
                        {initialError
                          ? `Error: ${initialError}`
                          : 'This template may not have content configured yet.'}
                      </p>
                    </div>

                    {/* Sample content for demonstration */}
                    <div className='mt-8 p-4 bg-gray-50 rounded border-l-4 border-gray-300'>
                      <p className='text-sm text-[var(--custom-gray-medium)] mb-2'>
                        <strong>Template ID:</strong> {templateId}
                      </p>
                      <p className='text-sm text-[var(--custom-gray-medium)] mb-2'>
                        <strong>Document Type:</strong> {documentType}
                      </p>
                      <p className='text-sm text-[var(--custom-gray-medium)]'>
                        <strong>State:</strong> {documentState}
                      </p>
                    </div>

                    {/* Debug: Show populated user attributes */}
                    {userAttributes && (
                      <div className='mt-8 p-4 bg-blue-50 rounded border-l-4 border-blue-300'>
                        <h4 className='text-sm font-semibold text-blue-800 mb-2'>
                          Available User Information:
                        </h4>
                        <div className='grid grid-cols-2 gap-2 text-xs text-blue-700'>
                          <div>
                            <strong>Name:</strong> {userAttributes.given_name}{' '}
                            {userAttributes.family_name}
                          </div>
                          <div>
                            <strong>Email:</strong> {userAttributes.email}
                          </div>
                          <div>
                            <strong>Phone:</strong>{' '}
                            {userAttributes.phone_number || 'Not provided'}
                          </div>
                          <div>
                            <strong>Address:</strong>{' '}
                            {userAttributes.address || 'Not provided'}
                          </div>
                          <div>
                            <strong>Birth Date:</strong>{' '}
                            {userAttributes.birthdate || 'Not provided'}
                          </div>
                          <div>
                            <strong>Gender:</strong>{' '}
                            {userAttributes.gender || 'Not provided'}
                          </div>
                        </div>
                      </div>
                    )}
                  </>
                )}
              </div>
            </div>
          </CardContent>
          <CardFooter className='flex flex-col items-start space-y-4'>
            <div className='flex items-center space-x-2'>
              <Checkbox
                id='approve'
                checked={approved}
                onCheckedChange={handleApproveChange}
              />
              <label
                htmlFor='approve'
                className='text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70'
              >
                I have reviewed this document and approve it as accurate
              </label>
            </div>

            <div className='flex justify-between w-full'>
              <Button variant='outline' onClick={handleEdit}>
                Edit Information
              </Button>

              <Button onClick={handleSave} disabled={isSaving}>
                {isSaving ? 'Saving...' : 'Save Document'}
              </Button>
            </div>
          </CardFooter>
        </Card>

        <div className='bg-amber-50 p-6 rounded-lg border border-amber-100'>
          <h3 className='text-lg font-medium text-amber-800 mb-2'>
            Important Information
          </h3>
          <p className='text-amber-700 mb-4'>
            This is a preview of your legal document. Please review it carefully
            before approving.
          </p>
          <ul className='list-disc list-inside text-amber-700 space-y-2'>
            <li>
              This document is based on the information you provided during the
              interview
            </li>
            <li>Once approved, this document will be saved to your account</li>
            <li>
              You can make changes to your information and regenerate this
              document at any time
            </li>
            <li>
              For legal advice specific to your situation, please consult with
              an attorney
            </li>
          </ul>
        </div>
      </div>
    </div>
  );
}
