'use client';

import React, { useMemo, useState } from 'react';
import { useRouter } from 'next/navigation';
import { But<PERSON> } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { AlertCircle } from 'lucide-react';
import { useQuery } from '@tanstack/react-query';
import { fetchTemplates } from '@/utils/templates';

export default function DocumentCreatePage() {
  const router = useRouter();
  const [documentType, setDocumentType] = useState<string>('');
  const [error, setError] = useState<string | null>(null);

  const { data: templates = [], isLoading } = useQuery({
    queryKey: ['templates'],
    queryFn: fetchTemplates,
  });

  console.log('===> TEMPLATES', templates);

  // Mock user state data - in a real implementation, this would come from the user's profile
  const userState = 'California';

  const documentTypes = useMemo(() => {
    const filteredTemplatesByState = templates.filter(
      template => template.templateState === userState
    );
    return filteredTemplatesByState.map(template => {
      const templateType = template.type;
      return <SelectItem value={template.id}>{templateType}</SelectItem>;
    });
  }, [templates]);

  console.log('===> DOCUMENT TYPES', documentTypes);

  const handleDocumentTypeChange = (value: string) => {
    setDocumentType(value);
    setError(null);
  };

  const handleCreateDocument = () => {
    if (!documentType) {
      setError('Please select a document type');
      return;
    }

    // In a real implementation, this would call the backend to generate the document
    // For now, we'll just navigate to the preview page
    router.push(`/dashboard/member/documents/preview/${documentType}`);
  };

  return (
    <div className='container mx-auto py-8 px-4'>
      <div className='max-w-4xl mx-auto'>
        <div className='mb-8'>
          <h1 className='text-3xl font-bold text-black-6c mb-2'>
            Create Legal Documents
          </h1>
          <p className='text-[var(--custom-gray-medium)]'>
            Select the type of document you want to create. We'll use your
            interview responses to generate a personalized document.
          </p>
        </div>

        <Card className='mb-8'>
          <CardHeader>
            <CardTitle>Document Selection</CardTitle>
            <CardDescription>
              Choose the type of legal document you want to create
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className='space-y-6'>
              <div className='space-y-2'>
                <label className='text-sm font-medium'>Document Type</label>
                <Select
                  value={documentType}
                  onValueChange={handleDocumentTypeChange}
                >
                  <SelectTrigger className='w-full'>
                    <SelectValue placeholder='Select document type' />
                  </SelectTrigger>
                  <SelectContent>{documentTypes}</SelectContent>
                </Select>
              </div>

              <div className='space-y-2'>
                <label className='text-sm font-medium'>State</label>
                <div className='p-3 border rounded-md bg-gray-50'>
                  <p>{userState}</p>
                  <p className='text-xs text-[var(--custom-gray-medium)] mt-1'>
                    This is your state of residence from your profile. Documents
                    will be created according to {userState} law.
                  </p>
                </div>
              </div>

              {error && (
                <Alert variant='destructive'>
                  <AlertCircle className='h-4 w-4' />
                  <AlertTitle>Error</AlertTitle>
                  <AlertDescription>{error}</AlertDescription>
                </Alert>
              )}
            </div>
          </CardContent>
          <CardFooter className='flex justify-end'>
            <Button onClick={handleCreateDocument}>Create Document</Button>
          </CardFooter>
        </Card>

        <div className='bg-blue-50 p-6 rounded-lg border border-blue-100'>
          <h3 className='text-lg font-medium text-blue-800 mb-2'>
            About Document Generation
          </h3>
          <p className='text-blue-700 mb-4'>
            Our document generation system creates legally compliant documents
            based on your state's requirements and the information you provided
            during the interview process.
          </p>
          <ul className='list-disc list-inside text-blue-700 space-y-2'>
            <li>
              Documents are tailored to your specific needs and state laws
            </li>
            <li>You'll have a chance to review before finalizing</li>
            <li>
              All documents are stored securely and can be accessed at any time
            </li>
            <li>
              You'll be notified if state laws change that affect your documents
            </li>
          </ul>
        </div>
      </div>
    </div>
  );
}
