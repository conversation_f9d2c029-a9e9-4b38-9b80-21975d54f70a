'use client';

import { useState, useEffect } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { useRouter } from 'next/navigation';
import routes from '@/utils/routes';
import {
  EmergencyContact,
  DMSConfiguration,
} from '@/components/emergency/types';

interface LivingDocument {
  id: string;
  title: string;
  type: string;
  description: string;
  content: string;
  createdAt: string;
  lastUpdated: string;
  nextReview: string;
  reviewFrequency: string;
  status: 'current' | 'review-soon' | 'overdue';
}

// Mock data for demonstration
const mockContacts: EmergencyContact[] = [
  {
    id: '1',
    name: '<PERSON>',
    relationship: 'Sister',
    phone: '(*************',
    email: '<EMAIL>',
    type: 'Medical',
    status: 'Verified',
    isPrimary: true,
  },
  {
    id: '2',
    name: '<PERSON>',
    relationship: 'Friend',
    phone: '(*************',
    email: '<EMAIL>',
    type: 'Other',
    status: 'Pending',
  },
];

// Mock DMS configuration
const mockDMSConfig: DMSConfiguration = {
  id: '1',
  userId: 'user123',
  frequency: 'WEEKLY',
  communicationMethod: 'EMAIL',
  escalationProtocol: 'STANDARD',
  personalMessage:
    'Please check on my pets at 123 Main St. The key is with my neighbor.',
  status: 'ACTIVE',
  nextCheckIn: new Date(Date.now() + 5 * 24 * 60 * 60 * 1000).toISOString(),
  lastCheckIn: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000).toISOString(),
};

// Mock living documents
const mockLivingDocuments: LivingDocument[] = [
  {
    id: '1',
    title: 'Emergency Contacts',
    type: 'emergency-contacts',
    description: 'List of emergency contacts and their information',
    content:
      'Emergency Contact 1:\nName: John Doe\nPhone: (*************\nRelationship: Friend',
    createdAt: 'May 10, 2025',
    lastUpdated: 'June 15, 2025',
    nextReview: 'December 15, 2025',
    reviewFrequency: 'Every 6 months',
    status: 'current',
  },
  {
    id: '2',
    title: 'Pet Care Instructions',
    type: 'pet-care',
    description: 'Instructions for taking care of my pets',
    content: 'Pet: Max (Dog)\nBreed: Golden Retriever\nAge: 5 years',
    createdAt: 'April 5, 2025',
    lastUpdated: 'May 20, 2025',
    nextReview: 'August 20, 2025',
    reviewFrequency: 'Every 3 months',
    status: 'review-soon',
  },
];

export default function DashboardPage() {
  const router = useRouter();
  const [contacts, setContacts] = useState<EmergencyContact[]>([]);
  const [dmsConfig, setDmsConfig] = useState<DMSConfiguration | null>(null);
  const [, setLivingDocuments] = useState<LivingDocument[]>([]);

  useEffect(() => {
    // In a real implementation, this would fetch data from an API
    // For now, we'll use mock data
    console.log('Dashboard page loading...');
    setContacts(mockContacts);
    setDmsConfig(mockDMSConfig);
    setLivingDocuments(mockLivingDocuments);
  }, []);

  return (
    <div className='max-w-7xl mx-auto'>
      <h1 className='text-3xl font-bold mb-8 text-[var(--custom-gray-dark)]'>
        Welcome to Your Dashboard
      </h1>

      <div className='grid grid-cols-1 lg:grid-cols-2 gap-6'>
        <div className='bg-background p-6 rounded-lg shadow-sm h-full flex flex-col'>
          <div className='flex justify-between items-center mb-2'>
            <div>
              <h2 className='text-lg font-semibold'>Your Documents</h2>
              <p className='text-[var(--custom-gray-medium)] text-xs'>
                Manage your estate planning documents
              </p>
            </div>
            <Button
              variant='default'
              size='lg'
              onClick={() => router.push('/dashboard/member/interview/new')}
            >
              Start Interview
            </Button>
          </div>

          {/* Mock document status - in real implementation this would be dynamic */}
          <div className='space-y-2 mb-3 flex-grow'>
            <div className='bg-blue-50 border-l-4 border-blue-400 p-2 rounded-r-lg text-sm'>
              <p>📄 3 documents ready for review and signing</p>
            </div>
            <div className='bg-green-50 border-l-4 border-green-400 p-2 rounded-r-lg text-sm'>
              <p>✅ Will: Shipped to Welon Trust (Tracking: 1Z123456)</p>
            </div>
          </div>

          <div className='grid grid-cols-2 gap-2 mt-auto'>
            <Button
              variant='default'
              size='default'
              onClick={() => router.push(routes.documentsManage)}
            >
              Manage Documents
            </Button>
            <Button
              variant='outline'
              size='default'
              onClick={() => router.push('/dashboard/member/documents/create')}
            >
              Create New
            </Button>
            <Button
              variant='outline'
              size='default'
              onClick={() => router.push(routes.documentsManageReview)}
            >
              Review Documents
            </Button>
            <Button
              variant='outline'
              size='default'
              onClick={() => router.push(routes.documentsManageSign)}
            >
              Sign Documents
            </Button>
          </div>
        </div>

        {/* Shared Documents Section */}
        <div className='bg-background p-6 rounded-lg shadow-sm h-full flex flex-col'>
          <div className='flex justify-between items-center mb-2'>
            <div>
              <h2 className='text-lg font-semibold'>Shared Documents</h2>
              <p className='text-[var(--custom-gray-medium)] text-xs'>
                Collaborate on joint estate planning
              </p>
            </div>
            <Button
              variant='outline'
              size='default'
              onClick={() => router.push('/dashboard/member/shared-documents')}
            >
              View All
            </Button>
          </div>

          <div className='bg-blue-50 border-l-4 border-blue-400 p-2 rounded-r-lg mb-3 text-sm flex-grow'>
            <p>
              💡 Link accounts to create shared documents like joint trusts.
            </p>
          </div>

          <div className='flex gap-2 mt-auto'>
            <Button
              variant='secondary'
              size='default'
              className='flex-1'
              onClick={() =>
                router.push('/dashboard/member/settings/linked-accounts')
              }
            >
              Link Account
            </Button>
            <Button
              variant='default'
              size='default'
              className='flex-1'
              onClick={() => router.push('/dashboard/member/shared-documents')}
            >
              Shared Docs
            </Button>
          </div>
        </div>

        <div className='bg-background p-6 rounded-lg shadow-sm h-full flex flex-col'>
          <div className='flex justify-between items-center mb-2'>
            <div>
              <h2 className='text-lg font-semibold'>Emergency Contacts</h2>
              <p className='text-[var(--custom-gray-medium)] text-xs'>
                Manage who can access your information
              </p>
            </div>
            <Button
              variant='default'
              size='default'
              onClick={() =>
                router.push('/dashboard/member/emergency-contacts')
              }
            >
              {contacts.length > 0 ? 'Manage' : 'Add'} Contacts
            </Button>
          </div>

          {contacts.length > 0 ? (
            <div className='bg-green-50 border-l-4 border-green-400 p-2 rounded-r-lg text-sm'>
              <p>
                ✅ You've added {contacts.length} emergency contact
                {contacts.length !== 1 ? 's' : ''}.
              </p>
            </div>
          ) : (
            <div className='bg-amber-50 border-l-4 border-amber-400 p-2 rounded-r-lg text-sm'>
              <p>⚠️ No emergency contacts added yet.</p>
            </div>
          )}

          {contacts.length > 0 && contacts[0] && (
            <div className='mt-2'>
              <div className='flex items-center p-2 bg-gray-50 rounded-lg border border-gray-200'>
                <div className='w-6 h-6 rounded-full bg-dark-blue text-white flex items-center justify-center mr-2 text-xs'>
                  {contacts[0].name.charAt(0)}
                </div>
                <div>
                  <p className='font-medium text-sm'>{contacts[0].name}</p>
                  <p className='text-xs text-[var(--custom-gray-medium)]'>
                    {contacts[0].relationship}
                  </p>
                </div>
                {contacts[0].isPrimary && (
                  <span className='ml-auto bg-success-green text-white text-xs px-2 py-0.5 rounded-full'>
                    Primary
                  </span>
                )}
              </div>
            </div>
          )}
        </div>

        <div className='bg-background p-6 rounded-lg shadow-sm h-full flex flex-col'>
          <div className='flex justify-between items-center mb-2'>
            <div>
              <h2 className='text-lg font-semibold'>Dead Man's Switch</h2>
              <p className='text-[var(--custom-gray-medium)] text-xs'>
                Automated safety check system
              </p>
            </div>
            <Button
              variant='default'
              size='default'
              onClick={() => router.push('/dashboard/member/deadmanswitch')}
            >
              {dmsConfig
                ? dmsConfig.status === 'ACTIVE'
                  ? 'Manage'
                  : 'Resume'
                : 'Configure'}
            </Button>
          </div>

          {dmsConfig?.status === 'ACTIVE' ? (
            <div className='bg-green-50 border-l-4 border-green-400 p-2 rounded-r-lg text-sm'>
              <p>✅ Your Dead Man's Switch is active and working.</p>
            </div>
          ) : (
            <div className='bg-amber-50 border-l-4 border-amber-400 p-2 rounded-r-lg text-sm'>
              <p>⚠️ Your Dead Man's Switch is not configured yet.</p>
            </div>
          )}
        </div>

        <div className='bg-background p-6 rounded-lg shadow-sm h-full flex flex-col'>
          <div className='flex justify-between items-center mb-2'>
            <div>
              <h2 className='text-lg font-semibold'>Subscription & Billing</h2>
              <p className='text-[var(--custom-gray-medium)] text-xs'>
                Manage your subscription
              </p>
            </div>
            <Button
              variant='default'
              size='default'
              onClick={() => router.push('/dashboard/billing')}
            >
              Manage Billing
            </Button>
          </div>

          <div className='bg-gray-50 p-2 rounded-lg border border-gray-200 text-sm'>
            <div className='flex items-center justify-between'>
              <span className='font-medium'>Basic Plan</span>
              <span className='bg-dark-blue text-white px-2 py-0.5 rounded-full text-xs'>
                Monthly
              </span>
            </div>
          </div>
        </div>

        <div className='bg-background p-6 rounded-lg shadow-sm h-full flex flex-col'>
          <div className='flex justify-between items-center mb-2'>
            <div>
              <h2 className='text-lg font-semibold'>Account Linking</h2>
              <p className='text-[var(--custom-gray-medium)] text-xs'>
                Share access with trusted individuals
              </p>
            </div>
            <Button
              variant='default'
              size='default'
              onClick={() => router.push('/dashboard/account-linking')}
            >
              Manage Links
            </Button>
          </div>

          <div className='bg-amber-50 border-l-4 border-amber-400 p-2 rounded-r-lg text-sm'>
            <p>⚠️ No linked accounts yet.</p>
          </div>
        </div>

        <div className='bg-background p-6 rounded-lg shadow-sm h-full flex flex-col'>
          <div className='flex justify-between items-center mb-2'>
            <div>
              <h2 className='text-lg font-semibold'>Account Settings</h2>
              <p className='text-[var(--custom-gray-medium)] text-xs'>
                Manage your profile and preferences
              </p>
            </div>
            <Button
              variant='default'
              size='default'
              onClick={() => router.push('/dashboard/settings')}
            >
              Settings
            </Button>
          </div>

          <div className='bg-gray-50 p-2 rounded-lg border border-gray-200 text-sm'>
            <div className='flex items-center'>
              <div className='w-8 h-8 rounded-full bg-dark-blue text-white flex items-center justify-center mr-2 text-sm font-medium'>
                JD
              </div>
              <div>
                <p className='font-medium text-sm'>John Doe</p>
                <p className='text-xs text-[var(--custom-gray-medium)]'>
                  <EMAIL>
                </p>
              </div>
            </div>
          </div>
        </div>

        <div className='bg-background p-6 rounded-lg shadow-sm h-full flex flex-col'>
          <div className='flex justify-between items-center mb-2'>
            <div>
              <h2 className='text-lg font-semibold'>Educational Content</h2>
              <p className='text-[var(--custom-gray-medium)] text-xs'>
                Videos, articles & guides
              </p>
            </div>
            <Button
              variant='default'
              size='default'
              onClick={() => router.push('/dashboard/educational-content')}
            >
              Browse Content
            </Button>
          </div>

          <div className='bg-green-50 p-2 rounded-lg border border-green-200 text-sm'>
            <p>
              📚 Expand your knowledge with our comprehensive library of estate
              planning resources.
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}
