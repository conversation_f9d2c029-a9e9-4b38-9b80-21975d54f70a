'use client';

import { useAuth } from '@/app/context/AuthContext';
import { useRouter } from 'next/navigation';
import { useEffect } from 'react';
import { InterviewQuestionsAdmin } from './components/InterviewQuestionsAdmin';

export default function AdminPage() {
  const { user, loading } = useAuth();
  const router = useRouter();

  // Redirect if not authenticated
  useEffect(() => {
    if (!loading && !user) {
      router.push('/login');
    }
  }, [user, loading, router]);

  if (loading) {
    return (
      <div className='container mx-auto py-12 px-4'>
        <div className='flex items-center justify-center'>
          <p>Loading...</p>
        </div>
      </div>
    );
  }

  return (
    <div className='container mx-auto py-8 px-4'>
      <h1 className='text-3xl font-bold mb-8'>Interview Questions Admin</h1>
      <InterviewQuestionsAdmin />
    </div>
  );
}
