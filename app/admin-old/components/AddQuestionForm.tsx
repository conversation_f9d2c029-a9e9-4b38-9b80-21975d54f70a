'use client';

import { useState } from 'react';
import { generateClient } from 'aws-amplify/data';
import type { Schema } from '@/amplify/data/resource';
import { zodResolver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';
import * as z from 'zod';
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Switch } from '@/components/ui/switch';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { OptionsField } from './OptionsField';

// Define the form schema with Zod
const formSchema = z.object({
  questionTitle: z.string().min(1, 'Question title is required'),
  questionDescription: z.string().optional(),
  type: z.enum(['text', 'select', 'checkbox', 'radio']),
  options: z.any().optional(),
  order: z.coerce.number().int().optional(),
  isActive: z.boolean(), // Make this required without default
});

// Define FormValues type explicitly to ensure isActive is required
type FormValues = {
  questionTitle: string;
  questionDescription?: string;
  type: 'text' | 'select' | 'checkbox' | 'radio';
  options?: any;
  order?: number;
  isActive: boolean;
};

interface AddQuestionFormProps {
  onQuestionAdded: () => void;
}

export function AddQuestionForm({ onQuestionAdded }: AddQuestionFormProps) {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const client = generateClient<Schema>();

  const form = useForm<FormValues>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      questionTitle: '',
      questionDescription: '',
      type: 'text',
      options: [],
      order: undefined,
      isActive: true,
    },
  });

  const questionType = form.watch('type');
  const needsOptions = ['select', 'checkbox', 'radio'].includes(questionType);

  const onSubmit = async (data: FormValues) => {
    console.log('===> DATA', data);
    setIsSubmitting(true);
    try {
      // Format options as JSON if needed
      const formattedOptions = needsOptions
        ? JSON.stringify(data.options)
        : null;

      const { errors } = await client.models.InterviewQuestion.create({
        questionTitle: data.questionTitle,
        questionDescription: data.questionDescription || '',
        type: data.type,
        options: formattedOptions,
        order: data.order || 1,
        isActive: data.isActive,
        versionId: 'default-version', // TODO: Update to use proper versioning
      });

      console.log('===> ERRORS', errors);

      form.reset();
      onQuestionAdded();
    } catch (error) {
      console.error('Error adding question:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle>Add New Interview Question</CardTitle>
        <CardDescription>
          Create a new question for the interview process.
        </CardDescription>
      </CardHeader>
      <CardContent>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className='space-y-6'>
            <FormField
              control={form.control}
              name='questionTitle'
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Question Title*</FormLabel>
                  <FormControl>
                    <Input placeholder='Enter question title' {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name='questionDescription'
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Description</FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder='Enter additional details about the question'
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name='type'
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Question Type*</FormLabel>
                  <Select
                    onValueChange={field.onChange}
                    defaultValue={field.value}
                  >
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder='Select question type' />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      <SelectItem value='text'>Text</SelectItem>
                      <SelectItem value='select'>Select (Dropdown)</SelectItem>
                      <SelectItem value='checkbox'>Checkbox</SelectItem>
                      <SelectItem value='radio'>Radio</SelectItem>
                    </SelectContent>
                  </Select>
                  <FormDescription>
                    The type of input required for this question.
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            {needsOptions && (
              <OptionsField control={form.control} name='options' />
            )}

            <FormField
              control={form.control}
              name='order'
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Display Order</FormLabel>
                  <FormControl>
                    <Input
                      type='number'
                      placeholder='Enter display order (optional)'
                      {...field}
                      value={field.value || ''}
                    />
                  </FormControl>
                  <FormDescription>
                    The order in which this question appears (lower numbers
                    appear first).
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name='isActive'
              render={({ field }) => (
                <FormItem className='flex flex-row items-center justify-between rounded-lg border p-4'>
                  <div className='space-y-0.5'>
                    <FormLabel className='text-base'>Active Status</FormLabel>
                    <FormDescription>
                      Whether this question is currently active and visible.
                    </FormDescription>
                  </div>
                  <FormControl>
                    <Switch
                      checked={field.value}
                      onCheckedChange={field.onChange}
                    />
                  </FormControl>
                </FormItem>
              )}
            />

            <Button type='submit' disabled={isSubmitting}>
              {isSubmitting ? 'Saving...' : 'Add Question'}
            </Button>
          </form>
        </Form>
      </CardContent>
    </Card>
  );
}
