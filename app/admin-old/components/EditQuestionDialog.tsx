'use client';

import { useState, useEffect } from 'react';
import type { Schema } from '@/amplify/data/resource';
import { zodResolver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';
import * as z from 'zod';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Switch } from '@/components/ui/switch';
import { OptionsField } from './OptionsField';

// Define the form schema with Zod
const formSchema = z.object({
  questionTitle: z.string().min(1, 'Question title is required'),
  questionDescription: z.string().optional(),
  type: z.enum(['text', 'select', 'checkbox', 'radio']),
  options: z.any().optional(),
  order: z.coerce.number().int().optional(),
  isActive: z.boolean(), // Make this required without default
});

// Define FormValues type explicitly to ensure isActive is required
type FormValues = {
  questionTitle: string;
  questionDescription?: string;
  type: 'text' | 'select' | 'checkbox' | 'radio';
  options?: any;
  order?: number;
  isActive: boolean;
};

interface EditQuestionDialogProps {
  question: Schema['InterviewQuestion']['type'];
  open: boolean;
  onClose: () => void;
  onUpdate: (
    id: string,
    updatedQuestion: Partial<Schema['InterviewQuestion']['type']>
  ) => void;
}

export function EditQuestionDialog({
  question,
  open,
  onClose,
  onUpdate,
}: EditQuestionDialogProps) {
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Parse options if they're stored as a string
  const parseOptions = (options: any) => {
    if (!options) return null;
    if (Array.isArray(options)) return options;

    try {
      return typeof options === 'string' ? JSON.parse(options) : options;
    } catch (e) {
      console.error('Error parsing options:', e);
      return null;
    }
  };

  const form = useForm<FormValues>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      questionTitle: question.questionTitle,
      questionDescription: question.questionDescription || '',
      type: question.type as any,
      options: parseOptions(question.options),
      order: question.order ?? undefined, // Convert null to undefined
      isActive: question.isActive ?? true, // Ensure isActive is boolean
    },
  });

  // Reset form when question changes
  useEffect(() => {
    form.reset({
      questionTitle: question.questionTitle,
      questionDescription: question.questionDescription || '',
      type: question.type as any,
      options: parseOptions(question.options),
      order: question.order ?? undefined, // Convert null to undefined
      isActive: question.isActive ?? true, // Ensure isActive is boolean
    });
  }, [question, form]);

  const questionType = form.watch('type');
  const needsOptions = ['select', 'checkbox', 'radio'].includes(questionType);

  const onSubmit = async (data: FormValues) => {
    setIsSubmitting(true);
    try {
      // Format options as JSON if needed
      const formattedOptions = needsOptions
        ? JSON.stringify(data.options)
        : null;

      await onUpdate(question.id, {
        questionTitle: data.questionTitle,
        questionDescription: data.questionDescription || '',
        type: data.type,
        options: formattedOptions,
        order: data.order,
        isActive: data.isActive,
      });

      onClose();
    } catch (error) {
      console.error('Error updating question:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Dialog open={open} onOpenChange={open => !open && onClose()}>
      <DialogContent className='sm:max-w-[600px] max-h-[90vh] overflow-y-auto'>
        <DialogHeader>
          <DialogTitle>Edit Interview Question</DialogTitle>
          <DialogDescription>
            Update the details of this interview question.
          </DialogDescription>
        </DialogHeader>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className='space-y-6'>
            <FormField
              control={form.control}
              name='questionTitle'
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Question Title*</FormLabel>
                  <FormControl>
                    <Input placeholder='Enter question title' {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name='questionDescription'
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Description</FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder='Enter additional details about the question'
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name='type'
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Question Type*</FormLabel>
                  <Select
                    onValueChange={field.onChange}
                    defaultValue={field.value}
                  >
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder='Select question type' />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      <SelectItem value='text'>Text</SelectItem>
                      <SelectItem value='select'>Select (Dropdown)</SelectItem>
                      <SelectItem value='checkbox'>Checkbox</SelectItem>
                      <SelectItem value='radio'>Radio</SelectItem>
                    </SelectContent>
                  </Select>
                  <FormDescription>
                    The type of input required for this question.
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            {needsOptions && (
              <OptionsField control={form.control} name='options' />
            )}

            <FormField
              control={form.control}
              name='order'
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Display Order</FormLabel>
                  <FormControl>
                    <Input
                      type='number'
                      placeholder='Enter display order (optional)'
                      {...field}
                      value={field.value || ''}
                    />
                  </FormControl>
                  <FormDescription>
                    The order in which this question appears (lower numbers
                    appear first).
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name='isActive'
              render={({ field }) => (
                <FormItem className='flex flex-row items-center justify-between rounded-lg border p-4'>
                  <div className='space-y-0.5'>
                    <FormLabel className='text-base'>Active Status</FormLabel>
                    <FormDescription>
                      Whether this question is currently active and visible.
                    </FormDescription>
                  </div>
                  <FormControl>
                    <Switch
                      checked={field.value}
                      onCheckedChange={field.onChange}
                    />
                  </FormControl>
                </FormItem>
              )}
            />

            <DialogFooter>
              <Button type='button' variant='outline' onClick={onClose}>
                Cancel
              </Button>
              <Button type='submit' disabled={isSubmitting}>
                {isSubmitting ? 'Saving...' : 'Save Changes'}
              </Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}
