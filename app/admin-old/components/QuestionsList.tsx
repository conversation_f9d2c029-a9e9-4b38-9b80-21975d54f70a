'use client';

import { useState } from 'react';
import type { Schema } from '@/amplify/data/resource';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { Button } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { MoreHorizontal, Pencil, Trash2 } from 'lucide-react';
import { EditQuestionDialog } from './EditQuestionDialog';
import { Badge } from '@/components/ui/badge';

interface QuestionsListProps {
  questions: Schema['InterviewQuestion']['type'][];
  isLoading: boolean;
  onDelete: (id: string) => void;
  onUpdate: (
    id: string,
    updatedQuestion: Partial<Schema['InterviewQuestion']['type']>
  ) => void;
}

export function QuestionsList({
  questions,
  isLoading,
  onDelete,
  onUpdate,
}: QuestionsListProps) {
  const [editingQuestion, setEditingQuestion] = useState<
    Schema['InterviewQuestion']['type'] | null
  >(null);

  const handleEdit = (question: Schema['InterviewQuestion']['type']) => {
    setEditingQuestion(question);
  };

  const handleCloseDialog = () => {
    setEditingQuestion(null);
  };

  const formatOptions = (options: any) => {
    if (!options) return 'None';

    try {
      const parsedOptions =
        typeof options === 'string' ? JSON.parse(options) : options;
      if (Array.isArray(parsedOptions)) {
        return parsedOptions.join(', ');
      }
      return JSON.stringify(parsedOptions);
    } catch (e) {
      return String(options);
    }
  };

  if (isLoading) {
    return <div>Loading questions...</div>;
  }

  return (
    <div>
      <div className='rounded-md border'>
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Title</TableHead>
              <TableHead>Type</TableHead>
              <TableHead>Description</TableHead>
              <TableHead>Options</TableHead>
              <TableHead>Order</TableHead>
              <TableHead>Status</TableHead>
              <TableHead className='text-right'>Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {questions.length === 0 ? (
              <TableRow>
                <TableCell colSpan={7} className='h-24 text-center'>
                  No questions found.
                </TableCell>
              </TableRow>
            ) : (
              questions.map(question => (
                <TableRow key={question.id}>
                  <TableCell className='font-medium'>
                    {question.questionTitle}
                  </TableCell>
                  <TableCell>{question.type}</TableCell>
                  <TableCell className='max-w-xs truncate'>
                    {question.questionDescription || 'N/A'}
                  </TableCell>
                  <TableCell className='max-w-xs truncate'>
                    {formatOptions(question.options)}
                  </TableCell>
                  <TableCell>{question.order || 'N/A'}</TableCell>
                  <TableCell>
                    <Badge
                      variant={question.isActive ? 'default' : 'secondary'}
                    >
                      {question.isActive ? 'Active' : 'Inactive'}
                    </Badge>
                  </TableCell>
                  <TableCell className='text-right'>
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant='ghost' className='h-8 w-8 p-0'>
                          <span className='sr-only'>Open menu</span>
                          <MoreHorizontal className='h-4 w-4' />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align='end'>
                        <DropdownMenuLabel>Actions</DropdownMenuLabel>
                        <DropdownMenuSeparator />
                        <DropdownMenuItem onClick={() => handleEdit(question)}>
                          <Pencil className='mr-2 h-4 w-4' />
                          Edit
                        </DropdownMenuItem>
                        <DropdownMenuItem
                          onClick={() => onDelete(question.id)}
                          className='text-destructive focus:text-destructive'
                        >
                          <Trash2 className='mr-2 h-4 w-4' />
                          Delete
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </TableCell>
                </TableRow>
              ))
            )}
          </TableBody>
        </Table>
      </div>

      {editingQuestion && (
        <EditQuestionDialog
          question={editingQuestion}
          open={!!editingQuestion}
          onClose={handleCloseDialog}
          onUpdate={onUpdate}
        />
      )}
    </div>
  );
}
