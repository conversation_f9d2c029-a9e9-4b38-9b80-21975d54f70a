'use client';

import { useState, useEffect } from 'react';
import { generateClient } from 'aws-amplify/data';
import type { Schema } from '@/amplify/data/resource';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { QuestionsList } from './QuestionsList';
import { AddQuestionForm } from './AddQuestionForm';

export function InterviewQuestionsAdmin() {
  const [isLoading, setIsLoading] = useState(true);
  const [questions, setQuestions] = useState<
    Schema['InterviewQuestion']['type'][]
  >([]);
  const client = generateClient<Schema>();

  const fetchQuestions = async () => {
    setIsLoading(true);
    try {
      const { data, errors } = await client.models.InterviewQuestion.list();
      console.log('===> DATA', data);
      console.log('===> Errors', errors);
      setQuestions(data);
    } catch (error) {
      console.error('Error fetching questions:', error);
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetchQuestions();
  }, []);

  const handleQuestionAdded = () => {
    fetchQuestions();
  };

  const handleQuestionDeleted = async (id: string) => {
    try {
      await client.models.InterviewQuestion.delete({ id });
      setQuestions(questions.filter(q => q.id !== id));
    } catch (error) {
      console.error('Error deleting question:', error);
    }
  };

  const handleQuestionUpdated = async (
    id: string,
    updatedQuestion: Partial<Schema['InterviewQuestion']['type']>
  ) => {
    try {
      console.error('===> Updated', updatedQuestion);
      const { errors } = await client.models.InterviewQuestion.update({
        id,
        ...updatedQuestion,
      });
      console.log('===> ERRORS', errors);
      fetchQuestions();
    } catch (error) {
      console.error('Error updating question:', error);
    }
  };

  return (
    <Tabs defaultValue='list' className='w-full'>
      <TabsList className='mb-6'>
        <TabsTrigger value='list'>Questions List</TabsTrigger>
        <TabsTrigger value='add'>Add New Question</TabsTrigger>
      </TabsList>
      <TabsContent value='list'>
        <QuestionsList
          questions={questions}
          isLoading={isLoading}
          onDelete={handleQuestionDeleted}
          onUpdate={handleQuestionUpdated}
        />
      </TabsContent>
      <TabsContent value='add'>
        <AddQuestionForm onQuestionAdded={handleQuestionAdded} />
      </TabsContent>
    </Tabs>
  );
}
