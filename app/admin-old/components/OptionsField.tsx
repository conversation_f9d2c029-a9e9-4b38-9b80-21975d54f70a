'use client';

import { useState } from 'react';
import { Control } from 'react-hook-form';
import {
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Plus, X } from 'lucide-react';

interface OptionsFieldProps {
  control: Control<any>;
  name: string;
}

export function OptionsField({ control, name }: OptionsFieldProps) {
  const [newOption, setNewOption] = useState('');

  return (
    <FormField
      control={control}
      name={name}
      render={({ field }) => {
        const options = Array.isArray(field.value) ? field.value : [];

        const addOption = () => {
          if (newOption.trim() !== '') {
            field.onChange([...options, newOption.trim()]);
            setNewOption('');
          }
        };

        const removeOption = (index: number) => {
          const newOptions = [...options];
          newOptions.splice(index, 1);
          field.onChange(newOptions);
        };

        const handleKeyDown = (e: React.KeyboardEvent) => {
          if (e.key === 'Enter') {
            e.preventDefault();
            addOption();
          }
        };

        return (
          <FormItem>
            <FormLabel>Options</FormLabel>
            <div className='space-y-4'>
              <div className='flex gap-2'>
                <FormControl>
                  <Input
                    placeholder='Add an option'
                    value={newOption}
                    onChange={e => setNewOption(e.target.value)}
                    onKeyDown={handleKeyDown}
                  />
                </FormControl>
                <Button
                  type='button'
                  variant='outline'
                  size='icon'
                  onClick={addOption}
                >
                  <Plus className='h-4 w-4' />
                </Button>
              </div>

              {options.length > 0 ? (
                <div className='space-y-2'>
                  {options.map((option: string, index: number) => (
                    <div
                      key={index}
                      className='flex items-center justify-between rounded-md border px-3 py-2'
                    >
                      <span>{option}</span>
                      <Button
                        type='button'
                        variant='ghost'
                        size='icon'
                        onClick={() => removeOption(index)}
                      >
                        <X className='h-4 w-4' />
                      </Button>
                    </div>
                  ))}
                </div>
              ) : (
                <div className='text-sm text-muted-foreground'>
                  No options added yet.
                </div>
              )}
            </div>
            <FormDescription>
              Add options for select, checkbox, or radio question types.
            </FormDescription>
            <FormMessage />
          </FormItem>
        );
      }}
    />
  );
}
