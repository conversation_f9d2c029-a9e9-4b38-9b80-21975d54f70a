const routes = {
  home: '/',
  profile: '/profile',
  dashboard: '/dashboard',
  onboarding: '/onboarding',
  interview: '/member/interview',
  documents: '/documents',
  documentsManage: '/documents',
  documentsManageReview: '/documents?filter=ready_for_review',
  documentsManageSign: '/documents?filter=ready_for_signing',
  documentsReview: '/documents/review',
  documentsSign: '/documents/sign',
  emergencyContacts: '/member/emergency-contacts',
  livingDocuments: '/member/living-documents',
  livingDocumentsCreate: '/member/living-documents/create',
  vault: '/member/vault',
  login: '/login',
  emergency: '/emergency',
  admin: {
    dashboard: '/admin/dashboard',
    users: '/admin/users',
    roles: '/admin/roles',
    contentAnalytics: '/admin/content-analytics',
    content: '/admin/content',
    notifications: '/admin/notifications',
    interviewBuilder: '/admin/interview-builder',
    interviewBuilderCreate: '/admin/interview-builder/create',
    interviewBuilderEdit: (id: string) => `/admin/interview-builder/edit/${id}`,
    attorneys: '/admin/attorneys',
    billing: '/admin/billing',
    reports: '/admin/reports',
    settings: '/admin/settings',
    templates: '/admin/templates',
    newTemplate: '/admin/templates/edit/new',
    templatesEdit: (id: string) => `/admin/templates/edit/${id}`,
    templatesHistory: (id: string) => `/admin/templates/history/${id}`,
    templatesPropagate: '/admin/templates/propagate',
    legalUpdates: '/admin/legal-updates',
    quarterlyReview: '/admin/quarterly-review',
    auditDocuments: '/admin/audit/documents',
    emergency: '/admin/emergency',
  },
  welon: {
    dashboard: '/welon/dashboard',
    documents: '/welon/documents',
    emergency: '/welon/emergency',
    submitEvidence: '/welon/submit-evidence',
    uploadDocuments: '/welon/upload-documents',
    notifications: '/welon/notifications',
    emergencyDocuments: '/welon/notifications',
  },
  adminUsers: '/admin/users',
  adminUsersCreate: '/admin/users/create',
  adminUsersEdit: (id: string) => `/admin/users/edit/${id}`,
};

export default routes;
