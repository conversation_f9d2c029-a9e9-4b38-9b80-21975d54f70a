'use client';

import React, {
  createContext,
  useContext,
  useState,
  useEffect,
  useCallback,
  ReactNode,
} from 'react';
import {
  Notification,
  NotificationPreferences,
  NotificationFilters,
  NotificationContextType,
  NotificationStatus,
} from '@/types/notifications';
import { notificationApi } from '@/lib/api/notifications';
// import { useRole } from '@/lib/roles/role-context';

const NotificationContext = createContext<NotificationContextType | undefined>(
  undefined
);

interface NotificationProviderProps {
  children: ReactNode;
}

export function NotificationProvider({ children }: NotificationProviderProps) {
  const [notifications, setNotifications] = useState<Notification[]>([]);
  const [unreadCount, setUnreadCount] = useState(0);
  const [preferences, setPreferences] =
    useState<NotificationPreferences | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // const { userContext } = useRole();
  const userContext = { role: 'member' }; // Mock for now

  // Get current user ID based on role context
  const getCurrentUserId = useCallback(() => {
    if (!userContext) return null;

    // In a real app, this would come from authentication
    // For now, we'll use mock user IDs based on role
    switch (userContext.role) {
      case 'administrator':
        return 'user-admin-001';
      case 'welon_trust':
        return 'user-welon-001';
      case 'linked_account':
        return 'user-linked-001';
      default:
        return 'user-member-001';
    }
  }, [userContext]);

  // Fetch notifications for the current user
  const fetchNotifications = useCallback(
    async (filters?: NotificationFilters) => {
      const userId = getCurrentUserId();
      if (!userId) return;

      setIsLoading(true);
      setError(null);

      try {
        const response = await notificationApi.getNotificationHistory({
          userId,
          filters,
          limit: 50,
          offset: 0,
        });

        setNotifications(response.notifications);

        // Update unread count
        const unread = response.notifications.filter(
          n => n.status !== NotificationStatus.READ
        ).length;
        setUnreadCount(unread);
      } catch (err) {
        setError(
          err instanceof Error ? err.message : 'Failed to fetch notifications'
        );
        console.error('Error fetching notifications:', err);
      } finally {
        setIsLoading(false);
      }
    },
    [getCurrentUserId]
  );

  // Fetch notification preferences
  const fetchPreferences = useCallback(async () => {
    const userId = getCurrentUserId();
    if (!userId) return;

    try {
      const userPreferences =
        await notificationApi.getNotificationPreferences(userId);
      setPreferences(userPreferences);
    } catch (err) {
      console.error('Error fetching notification preferences:', err);
    }
  }, [getCurrentUserId]);

  // Mark a notification as read
  const markAsRead = useCallback(async (notificationId: string) => {
    try {
      await notificationApi.markNotificationAsRead(notificationId);

      // Update local state
      setNotifications(prev =>
        prev.map(notification =>
          notification.id === notificationId
            ? {
                ...notification,
                status: NotificationStatus.READ,
                readAt: new Date().toISOString(),
                updatedAt: new Date().toISOString(),
              }
            : notification
        )
      );

      // Update unread count
      setUnreadCount(prev => Math.max(0, prev - 1));
    } catch (err) {
      setError(
        err instanceof Error
          ? err.message
          : 'Failed to mark notification as read'
      );
      throw err;
    }
  }, []);

  // Mark all notifications as read
  const markAllAsRead = useCallback(async () => {
    const userId = getCurrentUserId();
    if (!userId) return;

    try {
      await notificationApi.markAllNotificationsAsRead(userId);

      // Update local state
      setNotifications(prev =>
        prev.map(notification => ({
          ...notification,
          status: NotificationStatus.READ,
          readAt: notification.readAt || new Date().toISOString(),
          updatedAt: new Date().toISOString(),
        }))
      );

      setUnreadCount(0);
    } catch (err) {
      setError(
        err instanceof Error
          ? err.message
          : 'Failed to mark all notifications as read'
      );
      throw err;
    }
  }, [getCurrentUserId]);

  // Delete a notification
  const deleteNotification = useCallback(
    async (notificationId: string) => {
      try {
        await notificationApi.deleteNotification(notificationId);

        // Update local state
        const notification = notifications.find(n => n.id === notificationId);
        setNotifications(prev => prev.filter(n => n.id !== notificationId));

        // Update unread count if the deleted notification was unread
        if (notification && notification.status !== NotificationStatus.READ) {
          setUnreadCount(prev => Math.max(0, prev - 1));
        }
      } catch (err) {
        setError(
          err instanceof Error ? err.message : 'Failed to delete notification'
        );
        throw err;
      }
    },
    [notifications]
  );

  // Update notification preferences
  const updatePreferences = useCallback(
    async (newPreferences: Partial<NotificationPreferences['preferences']>) => {
      const userId = getCurrentUserId();
      if (!userId) return;

      try {
        const response = await notificationApi.updateNotificationPreferences({
          userId,
          preferences: newPreferences,
        });

        if (response.success && preferences) {
          setPreferences({
            ...preferences,
            preferences: {
              ...preferences.preferences,
              ...newPreferences,
            },
            updatedAt: new Date().toISOString(),
          });
        }
      } catch (err) {
        setError(
          err instanceof Error
            ? err.message
            : 'Failed to update notification preferences'
        );
        throw err;
      }
    },
    [getCurrentUserId, preferences]
  );

  // Refresh notifications (refetch from server)
  const refreshNotifications = useCallback(async () => {
    await fetchNotifications();
  }, [fetchNotifications]);

  // Initialize notifications and preferences when user context changes
  useEffect(() => {
    if (userContext) {
      fetchNotifications();
      fetchPreferences();
    } else {
      // Clear state when no user
      setNotifications([]);
      setUnreadCount(0);
      setPreferences(null);
    }
  }, []);

  // Set up periodic refresh for real-time updates (every 30 seconds)
  useEffect(() => {
    if (!userContext) return;

    const interval = setInterval(() => {
      fetchNotifications();
    }, 30000); // 30 seconds

    return () => clearInterval(interval);
  }, []);

  const value: NotificationContextType = {
    notifications,
    unreadCount,
    preferences,
    isLoading,
    error,
    markAsRead,
    markAllAsRead,
    deleteNotification,
    updatePreferences,
    fetchNotifications,
    refreshNotifications,
  };

  return (
    <NotificationContext.Provider value={value}>
      {children}
    </NotificationContext.Provider>
  );
}

export function useNotifications(): NotificationContextType {
  const context = useContext(NotificationContext);
  if (context === undefined) {
    throw new Error(
      'useNotifications must be used within a NotificationProvider'
    );
  }
  return context;
}
