import {
  Notification,
  NotificationFilters,
  NotificationPreferences,
  SendNotificationRequest,
  SendNotificationResponse,
  GetNotificationHistoryRequest,
  GetNotificationHistoryResponse,
  UpdateNotificationPreferencesRequest,
  UpdateNotificationPreferencesResponse,
  NotificationStatus,
  NotificationCategory,
  DeliveryChannel,
} from '@/types/notifications';

import {
  mockNotifications,
  mockNotificationPreferences,
  getNotificationsForUser,
  getUnreadCountForUser,
} from '@/mock/notifications';

/**
 * Mock API client for notifications - in production this would use AWS Amplify/AppSync
 */
const mockClient = {
  graphql: async ({ query, variables }: { query: string; variables?: any }) => {
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 300));

    // Mock responses based on query type
    if (query.includes('getNotificationHistory')) {
      const { userId, filters, limit = 50, offset = 0 } = variables || {};
      let notifications = getNotificationsForUser(userId);

      // Apply filters
      if (filters) {
        if (filters.category) {
          notifications = notifications.filter(
            n => n.category === filters.category
          );
        }
        if (filters.priority) {
          notifications = notifications.filter(
            n => n.priority === filters.priority
          );
        }
        if (filters.status) {
          notifications = notifications.filter(
            n => n.status === filters.status
          );
        }
        if (filters.eventType) {
          notifications = notifications.filter(
            n => n.eventType === filters.eventType
          );
        }
        if (filters.unreadOnly) {
          notifications = notifications.filter(
            n => n.status !== NotificationStatus.READ
          );
        }
        if (filters.dateFrom) {
          notifications = notifications.filter(
            n => new Date(n.createdAt) >= new Date(filters.dateFrom)
          );
        }
        if (filters.dateTo) {
          notifications = notifications.filter(
            n => new Date(n.createdAt) <= new Date(filters.dateTo)
          );
        }
      }

      // Sort by creation date (newest first)
      notifications.sort(
        (a, b) =>
          new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()
      );

      // Apply pagination
      const paginatedNotifications = notifications.slice(
        offset,
        offset + limit
      );

      return {
        data: {
          getNotificationHistory: {
            notifications: paginatedNotifications,
            total: notifications.length,
            hasMore: offset + limit < notifications.length,
          },
        },
      };
    }

    if (query.includes('getNotificationPreferences')) {
      return {
        data: {
          getNotificationPreferences: mockNotificationPreferences,
        },
      };
    }

    if (query.includes('markNotificationAsRead')) {
      const { notificationId } = variables || {};
      const notification = mockNotifications.find(n => n.id === notificationId);
      if (notification) {
        notification.status = NotificationStatus.READ;
        notification.readAt = new Date().toISOString();
        notification.updatedAt = new Date().toISOString();
      }
      return {
        data: {
          markNotificationAsRead: {
            success: true,
            notification,
          },
        },
      };
    }

    if (query.includes('markAllNotificationsAsRead')) {
      const { userId } = variables || {};
      const userNotifications = mockNotifications.filter(
        n => n.userId === userId
      );
      userNotifications.forEach(notification => {
        if (notification.status !== NotificationStatus.READ) {
          notification.status = NotificationStatus.READ;
          notification.readAt = new Date().toISOString();
          notification.updatedAt = new Date().toISOString();
        }
      });
      return {
        data: {
          markAllNotificationsAsRead: {
            success: true,
            count: userNotifications.length,
          },
        },
      };
    }

    if (query.includes('deleteNotification')) {
      const { notificationId } = variables || {};
      const index = mockNotifications.findIndex(n => n.id === notificationId);
      if (index !== -1) {
        mockNotifications.splice(index, 1);
      }
      return {
        data: {
          deleteNotification: {
            success: true,
          },
        },
      };
    }

    if (query.includes('updateNotificationPreferences')) {
      const { userId, preferences } = variables || {};
      // Update mock preferences
      Object.assign(mockNotificationPreferences.preferences, preferences);
      mockNotificationPreferences.updatedAt = new Date().toISOString();

      return {
        data: {
          updateNotificationPreferences: {
            success: true,
            preferences: mockNotificationPreferences,
          },
        },
      };
    }

    if (query.includes('sendNotification')) {
      const { userId, eventType, message, priority, metadata } =
        variables || {};
      const newNotification: Notification = {
        id: `notif-${Date.now()}`,
        userId,
        eventType,
        category:
          priority === 'high'
            ? NotificationCategory.URGENT
            : NotificationCategory.ACTION_REQUIRED,
        priority,
        title: `New ${eventType.replace('_', ' ')} Notification`,
        message,
        status: NotificationStatus.SENT,
        channels: [DeliveryChannel.EMAIL, DeliveryChannel.IN_APP],
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        metadata,
      };

      mockNotifications.unshift(newNotification);

      return {
        data: {
          sendNotification: {
            success: true,
            notificationId: newNotification.id,
          },
        },
      };
    }

    // Default response
    return {
      data: null,
      errors: [{ message: 'Unknown query' }],
    };
  },
};

// GraphQL queries and mutations
const GET_NOTIFICATION_HISTORY = `
  query GetNotificationHistory($userId: ID!, $filters: NotificationFiltersInput, $limit: Int, $offset: Int) {
    getNotificationHistory(userId: $userId, filters: $filters, limit: $limit, offset: $offset) {
      notifications {
        id
        userId
        eventType
        category
        priority
        title
        message
        actionUrl
        actionText
        status
        channels
        createdAt
        updatedAt
        readAt
        metadata
      }
      total
      hasMore
    }
  }
`;

const GET_NOTIFICATION_PREFERENCES = `
  query GetNotificationPreferences($userId: ID!) {
    getNotificationPreferences(userId: $userId) {
      userId
      preferences
      updatedAt
    }
  }
`;

const MARK_NOTIFICATION_AS_READ = `
  mutation MarkNotificationAsRead($notificationId: ID!) {
    markNotificationAsRead(notificationId: $notificationId) {
      success
      notification {
        id
        status
        readAt
        updatedAt
      }
    }
  }
`;

const MARK_ALL_NOTIFICATIONS_AS_READ = `
  mutation MarkAllNotificationsAsRead($userId: ID!) {
    markAllNotificationsAsRead(userId: $userId) {
      success
      count
    }
  }
`;

const DELETE_NOTIFICATION = `
  mutation DeleteNotification($notificationId: ID!) {
    deleteNotification(notificationId: $notificationId) {
      success
    }
  }
`;

const UPDATE_NOTIFICATION_PREFERENCES = `
  mutation UpdateNotificationPreferences($userId: ID!, $preferences: NotificationPreferencesInput!) {
    updateNotificationPreferences(userId: $userId, preferences: $preferences) {
      success
      preferences {
        userId
        preferences
        updatedAt
      }
    }
  }
`;

const SEND_NOTIFICATION = `
  mutation SendNotification($userId: ID!, $eventType: String!, $message: String!, $priority: String!, $metadata: JSON) {
    sendNotification(userId: $userId, eventType: $eventType, message: $message, priority: $priority, metadata: $metadata) {
      success
      notificationId
    }
  }
`;

// API functions
export const notificationApi = {
  // Get notification history for a user
  async getNotificationHistory(
    request: GetNotificationHistoryRequest
  ): Promise<GetNotificationHistoryResponse> {
    const response = await mockClient.graphql({
      query: GET_NOTIFICATION_HISTORY,
      variables: request,
    });

    if (response.data?.getNotificationHistory) {
      return response.data.getNotificationHistory;
    }

    throw new Error('Failed to fetch notification history');
  },

  // Get notification preferences for a user
  async getNotificationPreferences(
    userId: string
  ): Promise<NotificationPreferences> {
    const response = await mockClient.graphql({
      query: GET_NOTIFICATION_PREFERENCES,
      variables: { userId },
    });

    if (response.data?.getNotificationPreferences) {
      return response.data.getNotificationPreferences;
    }

    throw new Error('Failed to fetch notification preferences');
  },

  // Mark a notification as read
  async markNotificationAsRead(notificationId: string): Promise<void> {
    const response = await mockClient.graphql({
      query: MARK_NOTIFICATION_AS_READ,
      variables: { notificationId },
    });

    if (!response.data?.markNotificationAsRead?.success) {
      throw new Error('Failed to mark notification as read');
    }
  },

  // Mark all notifications as read for a user
  async markAllNotificationsAsRead(userId: string): Promise<void> {
    const response = await mockClient.graphql({
      query: MARK_ALL_NOTIFICATIONS_AS_READ,
      variables: { userId },
    });

    if (!response.data?.markAllNotificationsAsRead?.success) {
      throw new Error('Failed to mark all notifications as read');
    }
  },

  // Delete a notification
  async deleteNotification(notificationId: string): Promise<void> {
    const response = await mockClient.graphql({
      query: DELETE_NOTIFICATION,
      variables: { notificationId },
    });

    if (!response.data?.deleteNotification?.success) {
      throw new Error('Failed to delete notification');
    }
  },

  // Update notification preferences
  async updateNotificationPreferences(
    request: UpdateNotificationPreferencesRequest
  ): Promise<UpdateNotificationPreferencesResponse> {
    const response = await mockClient.graphql({
      query: UPDATE_NOTIFICATION_PREFERENCES,
      variables: request,
    });

    if (response.data?.updateNotificationPreferences?.success) {
      return response.data.updateNotificationPreferences;
    }

    throw new Error('Failed to update notification preferences');
  },

  // Send a notification
  async sendNotification(
    request: SendNotificationRequest
  ): Promise<SendNotificationResponse> {
    const response = await mockClient.graphql({
      query: SEND_NOTIFICATION,
      variables: request,
    });

    if (response.data?.sendNotification?.success) {
      return response.data.sendNotification;
    }

    throw new Error('Failed to send notification');
  },

  // Get unread count for a user (helper function)
  async getUnreadCount(userId: string): Promise<number> {
    return getUnreadCountForUser(userId);
  },
};
