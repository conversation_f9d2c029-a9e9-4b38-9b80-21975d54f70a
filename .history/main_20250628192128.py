import mcp
from mcp.client.streamable_http import streamablehttp_client
import json
import base64

config = {
  "jiraUrl": "https://childfreelegacy.atlassian.net/",
  "jiraApiToken": "ATATT3xFfGF0XtSgcmbKoJqaM7lH2la-YPCwLRfz0iptyzNi6gfVK-WZYpROo7kbR-7B2ASpdnftqhI01EHOD2sIlBirR9Mm9Q2Q3PadsoXhocoVm8EgsvylPKAqid9WwGdXDJz6__fzANmVpFzrRBZFR3RRswJpsl6m9vaA3zNZEVfsZrZ5fyE=E6D98902",
  "readOnlyMode": False,
  "confluenceUrl": "https://childfreelegacy.atlassian.net/",
  "jiraSslVerify": True,
  "confluenceSslVerify": True
}
# Encode config in base64
config_b64 = base64.b64encode(json.dumps(config).encode()).decode()
smithery_api_key = "9568d201-c510-48f2-a34f-26102bb6672c"

# Create server URL
url = f"https://server.smithery.ai/@ayasahmad/mcp-atlassian3/mcp?config={config_b64}&api_key={smithery_api_key}"

async def main():
    # Connect to the server using HTTP client
    async with streamablehttp_client(url) as (read_stream, write_stream, _):
        async with mcp.ClientSession(read_stream, write_stream) as session:
            # Initialize the connection
            await session.initialize()
            # List available tools
            tools_result = await session.list_tools()
            print(f"Available tools: {', '.join([t.name for t in tools_result.tools])}")

if __name__ == "__main__":
    import asyncio
    asyncio.run(main())