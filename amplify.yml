version: 1
backend:
  phases:
    preBuild:
      commands:
        - npm install -g pnpm
    build:
      commands:
        - echo “REACT_APP_CFLegacy_Dev_UserPoolID=$REACT_APP_CFLegacy_Dev_UserPoolID” >> .env
        - echo “REACT_APP_CFLegacy_Dev_IdentityPoolID =$REACT_APP_CFLegacy_Dev_IdentityPoolID” >> .env
        - echo “REACT_APP_CFLegacy_Dev_authRoleARN =$REACT_APP_CFLegacy_Dev_authRoleARN” >> .env
        - echo “REACT_APP_CFLegacy_Dev_unauthRoleARN=$REACT_APP_CFLegacy_Dev_unauthRoleARN” >> .env
        - echo “REACT_APP_CFLegacy_Dev_userPoolClientID=$REACT_APP_CFLegacy_Dev_userPoolClientID” >> .env
        - pnpm install --frozen-lockfile
        - npx ampx pipeline-deploy --branch $AWS_BRANCH --app-id $AWS_APP_ID
frontend:
  phases:
    preBuild:
      commands:
        - npm install -g pnpm
    build:
      commands:
        - pnpm run build
  artifacts:
    baseDirectory: .next
    files:
      - '**/*'
  cache:
    paths:
      - .next/cache/**/*
      - .pnpm-store/**/*
